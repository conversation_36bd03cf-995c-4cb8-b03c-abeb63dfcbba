import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import {
  SetIdentityMailFromDomainRequest,
  SetIdentityMailFromDomainResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESClientResolvedConfig,
} from "../SESClient";
export interface SetIdentityMailFromDomainCommandInput
  extends SetIdentityMailFromDomainRequest {}
export interface SetIdentityMailFromDomainCommandOutput
  extends SetIdentityMailFromDomainResponse,
    __MetadataBearer {}
export declare class SetIdentityMailFromDomainCommand extends $Command<
  SetIdentityMailFromDomainCommandInput,
  SetIdentityMailFromDomainCommandOutput,
  SESClientResolvedConfig
> {
  readonly input: SetIdentityMailFromDomainCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: SetIdentityMailFromDomainCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: SESClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<
    SetIdentityMailFromDomainCommandInput,
    SetIdentityMailFromDomainCommandOutput
  >;
  private serialize;
  private deserialize;
}
