import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { DescribeReceiptRuleRequest, DescribeReceiptRuleResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link DescribeReceiptRuleCommand}.
 */
export interface DescribeReceiptRuleCommandInput extends DescribeReceiptRuleRequest {
}
/**
 * @public
 *
 * The output of {@link DescribeReceiptRuleCommand}.
 */
export interface DescribeReceiptRuleCommandOutput extends DescribeReceiptRuleResponse, __MetadataBearer {
}
/**
 * @public
 * <p>Returns the details of the specified receipt rule.</p>
 *         <p>For information about setting up receipt rules, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email-receipt-rules.html">Amazon SES Developer
 *                 Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, DescribeReceiptRuleCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, DescribeReceiptRuleCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // DescribeReceiptRuleRequest
 *   RuleSetName: "STRING_VALUE", // required
 *   RuleName: "STRING_VALUE", // required
 * };
 * const command = new DescribeReceiptRuleCommand(input);
 * const response = await client.send(command);
 * // { // DescribeReceiptRuleResponse
 * //   Rule: { // ReceiptRule
 * //     Name: "STRING_VALUE", // required
 * //     Enabled: true || false,
 * //     TlsPolicy: "STRING_VALUE",
 * //     Recipients: [ // RecipientsList
 * //       "STRING_VALUE",
 * //     ],
 * //     Actions: [ // ReceiptActionsList
 * //       { // ReceiptAction
 * //         S3Action: { // S3Action
 * //           TopicArn: "STRING_VALUE",
 * //           BucketName: "STRING_VALUE", // required
 * //           ObjectKeyPrefix: "STRING_VALUE",
 * //           KmsKeyArn: "STRING_VALUE",
 * //         },
 * //         BounceAction: { // BounceAction
 * //           TopicArn: "STRING_VALUE",
 * //           SmtpReplyCode: "STRING_VALUE", // required
 * //           StatusCode: "STRING_VALUE",
 * //           Message: "STRING_VALUE", // required
 * //           Sender: "STRING_VALUE", // required
 * //         },
 * //         WorkmailAction: { // WorkmailAction
 * //           TopicArn: "STRING_VALUE",
 * //           OrganizationArn: "STRING_VALUE", // required
 * //         },
 * //         LambdaAction: { // LambdaAction
 * //           TopicArn: "STRING_VALUE",
 * //           FunctionArn: "STRING_VALUE", // required
 * //           InvocationType: "STRING_VALUE",
 * //         },
 * //         StopAction: { // StopAction
 * //           Scope: "STRING_VALUE", // required
 * //           TopicArn: "STRING_VALUE",
 * //         },
 * //         AddHeaderAction: { // AddHeaderAction
 * //           HeaderName: "STRING_VALUE", // required
 * //           HeaderValue: "STRING_VALUE", // required
 * //         },
 * //         SNSAction: { // SNSAction
 * //           TopicArn: "STRING_VALUE", // required
 * //           Encoding: "STRING_VALUE",
 * //         },
 * //       },
 * //     ],
 * //     ScanEnabled: true || false,
 * //   },
 * // };
 *
 * ```
 *
 * @param DescribeReceiptRuleCommandInput - {@link DescribeReceiptRuleCommandInput}
 * @returns {@link DescribeReceiptRuleCommandOutput}
 * @see {@link DescribeReceiptRuleCommandInput} for command's `input` shape.
 * @see {@link DescribeReceiptRuleCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link RuleDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule does not exist.</p>
 *
 * @throws {@link RuleSetDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule set does not exist.</p>
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example DescribeReceiptRule
 * ```javascript
 * // The following example returns the details of a receipt rule:
 * const input = {
 *   "RuleName": "MyRule",
 *   "RuleSetName": "MyRuleSet"
 * };
 * const command = new DescribeReceiptRuleCommand(input);
 * const response = await client.send(command);
 * /* response ==
 * {
 *   "Rule": {
 *     "Actions": [
 *       {
 *         "S3Action": {
 *           "BucketName": "MyBucket",
 *           "ObjectKeyPrefix": "email"
 *         }
 *       }
 *     ],
 *     "Enabled": true,
 *     "Name": "MyRule",
 *     "ScanEnabled": true,
 *     "TlsPolicy": "Optional"
 *   }
 * }
 * *\/
 * // example id: describereceiptrule-1469055813118
 * ```
 *
 */
export declare class DescribeReceiptRuleCommand extends $Command<DescribeReceiptRuleCommandInput, DescribeReceiptRuleCommandOutput, SESClientResolvedConfig> {
    readonly input: DescribeReceiptRuleCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: DescribeReceiptRuleCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<DescribeReceiptRuleCommandInput, DescribeReceiptRuleCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
