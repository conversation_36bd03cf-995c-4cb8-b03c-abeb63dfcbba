import { WaiterConfiguration, WaiterResult } from "@aws-sdk/util-waiter";
import { HeadBucketCommandInput } from "../commands/HeadBucketCommand";
import { S3Client } from "../S3Client";
export declare const waitForBucketExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadBucketCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilBucketExists: (
  params: WaiterConfiguration<S3Client>,
  input: HeadBucketCommandInput
) => Promise<WaiterResult>;
