import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import {
  VerifyDomainIdentityRequest,
  VerifyDomainIdentityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESClientResolvedConfig,
} from "../SESClient";
export interface VerifyDomainIdentityCommandInput
  extends VerifyDomainIdentityRequest {}
export interface VerifyDomainIdentityCommandOutput
  extends VerifyDomainIdentityResponse,
    __MetadataBearer {}
export declare class VerifyDomainIdentityCommand extends $Command<
  VerifyDomainIdentityCommandInput,
  VerifyDomainIdentityCommandOutput,
  SESClientResolvedConfig
> {
  readonly input: VerifyDomainIdentityCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: VerifyDomainIdentityCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: SESClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<
    VerifyDomainIdentityCommandInput,
    VerifyDomainIdentityCommandOutput
  >;
  private serialize;
  private deserialize;
}
