import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { VerifyDomainDkimRequest, VerifyDomainDkimResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link VerifyDomainDkimCommand}.
 */
export interface VerifyDomainDkimCommandInput extends VerifyDomainDkimRequest {
}
/**
 * @public
 *
 * The output of {@link VerifyDomainDkimCommand}.
 */
export interface VerifyDomainDkimCommandOutput extends VerifyDomainDkimResponse, __MetadataBearer {
}
/**
 * @public
 * <p>Returns a set of DKIM tokens for a domain identity.</p>
 *         <important>
 *             <p>When you execute the <code>VerifyDomainDkim</code> operation, the domain that you
 *                 specify is added to the list of identities that are associated with your account.
 *                 This is true even if you haven't already associated the domain with your account by
 *                 using the <code>VerifyDomainIdentity</code> operation. However, you can't send email
 *                 from the domain until you either successfully <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/verify-domains.html">verify it</a> or you
 *                 successfully <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html">set up DKIM for
 *                 it</a>.</p>
 *         </important>
 *         <p>You use the tokens that are generated by this operation to create CNAME records. When
 *             Amazon SES detects that you've added these records to the DNS configuration for a domain, you
 *             can start sending email from that domain. You can start sending email even if you
 *             haven't added the TXT record provided by the VerifyDomainIdentity operation to the DNS
 *             configuration for your domain. All email that you send from the domain is authenticated
 *             using DKIM.</p>
 *         <p>To create the CNAME records for DKIM authentication, use the following values:</p>
 *         <ul>
 *             <li>
 *                 <p>
 *                   <b>Name</b>:
 *                         <i>token</i>._domainkey.<i>example.com</i>
 *                </p>
 *             </li>
 *             <li>
 *                 <p>
 *                   <b>Type</b>: CNAME</p>
 *             </li>
 *             <li>
 *                 <p>
 *                   <b>Value</b>:
 *                     <i>token</i>.dkim.amazonses.com</p>
 *             </li>
 *          </ul>
 *         <p>In the preceding example, replace <i>token</i> with one of the tokens
 *             that are generated when you execute this operation. Replace
 *                 <i>example.com</i> with your domain. Repeat this process for each
 *             token that's generated by this operation.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, VerifyDomainDkimCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, VerifyDomainDkimCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // VerifyDomainDkimRequest
 *   Domain: "STRING_VALUE", // required
 * };
 * const command = new VerifyDomainDkimCommand(input);
 * const response = await client.send(command);
 * // { // VerifyDomainDkimResponse
 * //   DkimTokens: [ // VerificationTokenList // required
 * //     "STRING_VALUE",
 * //   ],
 * // };
 *
 * ```
 *
 * @param VerifyDomainDkimCommandInput - {@link VerifyDomainDkimCommandInput}
 * @returns {@link VerifyDomainDkimCommandOutput}
 * @see {@link VerifyDomainDkimCommandInput} for command's `input` shape.
 * @see {@link VerifyDomainDkimCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example VerifyDomainDkim
 * ```javascript
 * // The following example generates DKIM tokens for a domain that has been verified with Amazon SES:
 * const input = {
 *   "Domain": "example.com"
 * };
 * const command = new VerifyDomainDkimCommand(input);
 * const response = await client.send(command);
 * /* response ==
 * {
 *   "DkimTokens": [
 *     "EXAMPLEq76owjnks3lnluwg65scbemvw",
 *     "EXAMPLEi3dnsj67hstzaj673klariwx2",
 *     "EXAMPLEwfbtcukvimehexktmdtaz6naj"
 *   ]
 * }
 * *\/
 * // example id: verifydomaindkim-1469049503083
 * ```
 *
 */
export declare class VerifyDomainDkimCommand extends $Command<VerifyDomainDkimCommandInput, VerifyDomainDkimCommandOutput, SESClientResolvedConfig> {
    readonly input: VerifyDomainDkimCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: VerifyDomainDkimCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<VerifyDomainDkimCommandInput, VerifyDomainDkimCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
