const { MessageEmbed } = require('discord.js');
const db = require('pro.db');
const { prefix, owners } = require(`${process.cwd()}/config`);

module.exports = {
    name: "top",
    aliases: ["توب", "اغنياء", "ترتيب"],
    description: "عرض قائمة بأغنى الأشخاص في السيرفر",
    run: async (client, message, args) => {
        try {
            // الحصول على جميع البيانات من قاعدة البيانات
            const allData = db.all();
            
            // تصفية البيانات للحصول فقط على بيانات المال
            const moneyData = [];
            for (const key of Object.keys(allData)) {
                if (key.startsWith('money_')) {
                    const userId = key.split('_')[1];
                    moneyData.push({
                        userId: userId,
                        money: allData[key]
                    });
                }
            }
            
            // ترتيب البيانات تنازلياً حسب كمية المال
            moneyData.sort((a, b) => b.money - a.money);
            
            // اختيار أعلى 7 أشخاص فقط
            const topUsers = moneyData.slice(0, 7);
            
            // إنشاء محتوى الإمبيد
            let description = '';
            
            for (let i = 0; i < topUsers.length; i++) {
                try {
                    const user = await client.users.fetch(topUsers[i].userId);
                    const username = user ? user.username : 'مستخدم غير معروف';
                    
                    // تنسيق رقم المال بتنسيق علمي للأرقام الكبيرة
                    const formattedMoney = topUsers[i].money >= 1e9 
                        ? `$${(topUsers[i].money / 1e9).toFixed(2)}e+${topUsers[i].money.toString().length - 1}C`
                        : `$${topUsers[i].money.toLocaleString()}`;
                    
                    description += `**${i + 1}. ${username}**\n${formattedMoney}\n\n`;
                } catch (err) {
                    console.error(`Error fetching user ${topUsers[i].userId}:`, err);
                    description += `**${i + 1}. مستخدم غير معروف**\n$${topUsers[i].money.toLocaleString()}\n\n`;
                }
            }
            
            // إنشاء الإمبيد وإرساله
            const embed = new MessageEmbed()
                .setColor('#7289da')
                .setTitle('أغنى الأشخاص في السيرفر')
                .setDescription(description)
                .setFooter({ text: `${message.guild.name} • توب الأغنياء`, iconURL: message.guild.iconURL({ dynamic: true }) })
                .setTimestamp();
            
            // إرسال الإمبيد في قناة البنك إذا كانت محددة، وإلا في القناة الحالية
            const bankChannelId = db.get(`chatbank_${message.guild.id}`);
            if (bankChannelId) {
                const bankChannel = message.guild.channels.cache.get(bankChannelId);
                if (bankChannel) {
                    bankChannel.send({ embeds: [embed] });
                    if (message.channel.id !== bankChannelId) {
                        message.reply(`تم إرسال قائمة الأغنياء في <#${bankChannelId}>`);
                    }
                    return;
                }
            }
            
            // إرسال في القناة الحالية إذا لم تكن قناة البنك محددة
            message.channel.send({ embeds: [embed] });
        } catch (error) {
            console.error('Error in top command:', error);
            message.reply('حدث خطأ أثناء تنفيذ الأمر. الرجاء المحاولة مرة أخرى لاحقاً.');
        }
    }
};
