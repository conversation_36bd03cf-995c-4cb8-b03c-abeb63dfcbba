import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import {
  DeleteIdentityRequest,
  DeleteIdentityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESClientResolvedConfig,
} from "../SESClient";
export interface DeleteIdentityCommandInput extends DeleteIdentityRequest {}
export interface DeleteIdentityCommandOutput
  extends DeleteIdentityResponse,
    __MetadataBearer {}
export declare class DeleteIdentityCommand extends $Command<
  DeleteIdentityCommandInput,
  DeleteIdentityCommandOutput,
  SESClientResolvedConfig
> {
  readonly input: DeleteIdentityCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: DeleteIdentityCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: SESClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<DeleteIdentityCommandInput, DeleteIdentityCommandOutput>;
  private serialize;
  private deserialize;
}
