"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.paginateListCustomVerificationEmailTemplates = void 0;
const ListCustomVerificationEmailTemplatesCommand_1 = require("../commands/ListCustomVerificationEmailTemplatesCommand");
const SESClient_1 = require("../SESClient");
const makePagedClientRequest = async (client, input, ...args) => {
    return await client.send(new ListCustomVerificationEmailTemplatesCommand_1.ListCustomVerificationEmailTemplatesCommand(input), ...args);
};
async function* paginateListCustomVerificationEmailTemplates(config, input, ...additionalArguments) {
    let token = config.startingToken || undefined;
    let hasNext = true;
    let page;
    while (hasNext) {
        input.NextToken = token;
        input["MaxResults"] = config.pageSize;
        if (config.client instanceof SESClient_1.SESClient) {
            page = await makePagedClientRequest(config.client, input, ...additionalArguments);
        }
        else {
            throw new Error("Invalid client, expected SES | SESClient");
        }
        yield page;
        const prevToken = token;
        token = page.NextToken;
        hasNext = !!(token && (!config.stopOnSameToken || token !== prevToken));
    }
    return undefined;
}
exports.paginateListCustomVerificationEmailTemplates = paginateListCustomVerificationEmailTemplates;
