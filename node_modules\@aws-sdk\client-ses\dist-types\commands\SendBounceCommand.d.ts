import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __Metada<PERSON>Bearer, MiddlewareStack } from "@aws-sdk/types";
import { SendBounceRequest, SendBounceResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link SendBounceCommand}.
 */
export interface SendBounceCommandInput extends SendBounceRequest {
}
/**
 * @public
 *
 * The output of {@link SendBounceCommand}.
 */
export interface SendBounceCommandOutput extends SendBounceResponse, __MetadataBearer {
}
/**
 * @public
 * <p>Generates and sends a bounce message to the sender of an email you received through
 *             Amazon SES. You can only use this API on an email up to 24 hours after you receive it.</p>
 *         <note>
 *             <p>You cannot use this API to send generic bounces for mail that was not received by
 *                 Amazon SES.</p>
 *         </note>
 *         <p>For information about receiving email through Amazon SES, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email.html">Amazon SES
 *                 Developer Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, SendBounceCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, SendBounceCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // SendBounceRequest
 *   OriginalMessageId: "STRING_VALUE", // required
 *   BounceSender: "STRING_VALUE", // required
 *   Explanation: "STRING_VALUE",
 *   MessageDsn: { // MessageDsn
 *     ReportingMta: "STRING_VALUE", // required
 *     ArrivalDate: new Date("TIMESTAMP"),
 *     ExtensionFields: [ // ExtensionFieldList
 *       { // ExtensionField
 *         Name: "STRING_VALUE", // required
 *         Value: "STRING_VALUE", // required
 *       },
 *     ],
 *   },
 *   BouncedRecipientInfoList: [ // BouncedRecipientInfoList // required
 *     { // BouncedRecipientInfo
 *       Recipient: "STRING_VALUE", // required
 *       RecipientArn: "STRING_VALUE",
 *       BounceType: "STRING_VALUE",
 *       RecipientDsnFields: { // RecipientDsnFields
 *         FinalRecipient: "STRING_VALUE",
 *         Action: "STRING_VALUE", // required
 *         RemoteMta: "STRING_VALUE",
 *         Status: "STRING_VALUE", // required
 *         DiagnosticCode: "STRING_VALUE",
 *         LastAttemptDate: new Date("TIMESTAMP"),
 *         ExtensionFields: [
 *           {
 *             Name: "STRING_VALUE", // required
 *             Value: "STRING_VALUE", // required
 *           },
 *         ],
 *       },
 *     },
 *   ],
 *   BounceSenderArn: "STRING_VALUE",
 * };
 * const command = new SendBounceCommand(input);
 * const response = await client.send(command);
 * // { // SendBounceResponse
 * //   MessageId: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param SendBounceCommandInput - {@link SendBounceCommandInput}
 * @returns {@link SendBounceCommandOutput}
 * @see {@link SendBounceCommandInput} for command's `input` shape.
 * @see {@link SendBounceCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link MessageRejected} (client fault)
 *  <p>Indicates that the action failed, and the message could not be sent. Check the error
 *             stack for more information about what caused the error.</p>
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 */
export declare class SendBounceCommand extends $Command<SendBounceCommandInput, SendBounceCommandOutput, SESClientResolvedConfig> {
    readonly input: SendBounceCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: SendBounceCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<SendBounceCommandInput, SendBounceCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
