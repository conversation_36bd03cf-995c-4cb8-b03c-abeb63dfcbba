import { HttpHandlerOptions as __HttpHandlerOptions } from "@aws-sdk/types";
import {
  AbortMultipartUploadCommandInput,
  AbortMultipartUploadCommandOutput,
} from "./commands/AbortMultipartUploadCommand";
import {
  CompleteMultipartUploadCommandInput,
  CompleteMultipartUploadCommandOutput,
} from "./commands/CompleteMultipartUploadCommand";
import {
  CopyObjectCommandInput,
  CopyObjectCommandOutput,
} from "./commands/CopyObjectCommand";
import {
  CreateBucketCommandInput,
  CreateBucketCommandOutput,
} from "./commands/CreateBucketCommand";
import {
  CreateMultipartUploadCommandInput,
  CreateMultipartUploadCommandOutput,
} from "./commands/CreateMultipartUploadCommand";
import {
  DeleteBucketAnalyticsConfigurationCommandInput,
  DeleteBucketAnalyticsConfigurationCommandOutput,
} from "./commands/DeleteBucketAnalyticsConfigurationCommand";
import {
  DeleteBucketCommandInput,
  DeleteBucketCommandOutput,
} from "./commands/DeleteBucketCommand";
import {
  DeleteBucketCorsCommandInput,
  DeleteBucketCorsCommandOutput,
} from "./commands/DeleteBucketCorsCommand";
import {
  DeleteBucketEncryptionCommandInput,
  DeleteBucketEncryptionCommandOutput,
} from "./commands/DeleteBucketEncryptionCommand";
import {
  DeleteBucketIntelligentTieringConfigurationCommandInput,
  DeleteBucketIntelligentTieringConfigurationCommandOutput,
} from "./commands/DeleteBucketIntelligentTieringConfigurationCommand";
import {
  DeleteBucketInventoryConfigurationCommandInput,
  DeleteBucketInventoryConfigurationCommandOutput,
} from "./commands/DeleteBucketInventoryConfigurationCommand";
import {
  DeleteBucketLifecycleCommandInput,
  DeleteBucketLifecycleCommandOutput,
} from "./commands/DeleteBucketLifecycleCommand";
import {
  DeleteBucketMetricsConfigurationCommandInput,
  DeleteBucketMetricsConfigurationCommandOutput,
} from "./commands/DeleteBucketMetricsConfigurationCommand";
import {
  DeleteBucketOwnershipControlsCommandInput,
  DeleteBucketOwnershipControlsCommandOutput,
} from "./commands/DeleteBucketOwnershipControlsCommand";
import {
  DeleteBucketPolicyCommandInput,
  DeleteBucketPolicyCommandOutput,
} from "./commands/DeleteBucketPolicyCommand";
import {
  DeleteBucketReplicationCommandInput,
  DeleteBucketReplicationCommandOutput,
} from "./commands/DeleteBucketReplicationCommand";
import {
  DeleteBucketTaggingCommandInput,
  DeleteBucketTaggingCommandOutput,
} from "./commands/DeleteBucketTaggingCommand";
import {
  DeleteBucketWebsiteCommandInput,
  DeleteBucketWebsiteCommandOutput,
} from "./commands/DeleteBucketWebsiteCommand";
import {
  DeleteObjectCommandInput,
  DeleteObjectCommandOutput,
} from "./commands/DeleteObjectCommand";
import {
  DeleteObjectsCommandInput,
  DeleteObjectsCommandOutput,
} from "./commands/DeleteObjectsCommand";
import {
  DeleteObjectTaggingCommandInput,
  DeleteObjectTaggingCommandOutput,
} from "./commands/DeleteObjectTaggingCommand";
import {
  DeletePublicAccessBlockCommandInput,
  DeletePublicAccessBlockCommandOutput,
} from "./commands/DeletePublicAccessBlockCommand";
import {
  GetBucketAccelerateConfigurationCommandInput,
  GetBucketAccelerateConfigurationCommandOutput,
} from "./commands/GetBucketAccelerateConfigurationCommand";
import {
  GetBucketAclCommandInput,
  GetBucketAclCommandOutput,
} from "./commands/GetBucketAclCommand";
import {
  GetBucketAnalyticsConfigurationCommandInput,
  GetBucketAnalyticsConfigurationCommandOutput,
} from "./commands/GetBucketAnalyticsConfigurationCommand";
import {
  GetBucketCorsCommandInput,
  GetBucketCorsCommandOutput,
} from "./commands/GetBucketCorsCommand";
import {
  GetBucketEncryptionCommandInput,
  GetBucketEncryptionCommandOutput,
} from "./commands/GetBucketEncryptionCommand";
import {
  GetBucketIntelligentTieringConfigurationCommandInput,
  GetBucketIntelligentTieringConfigurationCommandOutput,
} from "./commands/GetBucketIntelligentTieringConfigurationCommand";
import {
  GetBucketInventoryConfigurationCommandInput,
  GetBucketInventoryConfigurationCommandOutput,
} from "./commands/GetBucketInventoryConfigurationCommand";
import {
  GetBucketLifecycleConfigurationCommandInput,
  GetBucketLifecycleConfigurationCommandOutput,
} from "./commands/GetBucketLifecycleConfigurationCommand";
import {
  GetBucketLocationCommandInput,
  GetBucketLocationCommandOutput,
} from "./commands/GetBucketLocationCommand";
import {
  GetBucketLoggingCommandInput,
  GetBucketLoggingCommandOutput,
} from "./commands/GetBucketLoggingCommand";
import {
  GetBucketMetricsConfigurationCommandInput,
  GetBucketMetricsConfigurationCommandOutput,
} from "./commands/GetBucketMetricsConfigurationCommand";
import {
  GetBucketNotificationConfigurationCommandInput,
  GetBucketNotificationConfigurationCommandOutput,
} from "./commands/GetBucketNotificationConfigurationCommand";
import {
  GetBucketOwnershipControlsCommandInput,
  GetBucketOwnershipControlsCommandOutput,
} from "./commands/GetBucketOwnershipControlsCommand";
import {
  GetBucketPolicyCommandInput,
  GetBucketPolicyCommandOutput,
} from "./commands/GetBucketPolicyCommand";
import {
  GetBucketPolicyStatusCommandInput,
  GetBucketPolicyStatusCommandOutput,
} from "./commands/GetBucketPolicyStatusCommand";
import {
  GetBucketReplicationCommandInput,
  GetBucketReplicationCommandOutput,
} from "./commands/GetBucketReplicationCommand";
import {
  GetBucketRequestPaymentCommandInput,
  GetBucketRequestPaymentCommandOutput,
} from "./commands/GetBucketRequestPaymentCommand";
import {
  GetBucketTaggingCommandInput,
  GetBucketTaggingCommandOutput,
} from "./commands/GetBucketTaggingCommand";
import {
  GetBucketVersioningCommandInput,
  GetBucketVersioningCommandOutput,
} from "./commands/GetBucketVersioningCommand";
import {
  GetBucketWebsiteCommandInput,
  GetBucketWebsiteCommandOutput,
} from "./commands/GetBucketWebsiteCommand";
import {
  GetObjectAclCommandInput,
  GetObjectAclCommandOutput,
} from "./commands/GetObjectAclCommand";
import {
  GetObjectAttributesCommandInput,
  GetObjectAttributesCommandOutput,
} from "./commands/GetObjectAttributesCommand";
import {
  GetObjectCommandInput,
  GetObjectCommandOutput,
} from "./commands/GetObjectCommand";
import {
  GetObjectLegalHoldCommandInput,
  GetObjectLegalHoldCommandOutput,
} from "./commands/GetObjectLegalHoldCommand";
import {
  GetObjectLockConfigurationCommandInput,
  GetObjectLockConfigurationCommandOutput,
} from "./commands/GetObjectLockConfigurationCommand";
import {
  GetObjectRetentionCommandInput,
  GetObjectRetentionCommandOutput,
} from "./commands/GetObjectRetentionCommand";
import {
  GetObjectTaggingCommandInput,
  GetObjectTaggingCommandOutput,
} from "./commands/GetObjectTaggingCommand";
import {
  GetObjectTorrentCommandInput,
  GetObjectTorrentCommandOutput,
} from "./commands/GetObjectTorrentCommand";
import {
  GetPublicAccessBlockCommandInput,
  GetPublicAccessBlockCommandOutput,
} from "./commands/GetPublicAccessBlockCommand";
import {
  HeadBucketCommandInput,
  HeadBucketCommandOutput,
} from "./commands/HeadBucketCommand";
import {
  HeadObjectCommandInput,
  HeadObjectCommandOutput,
} from "./commands/HeadObjectCommand";
import {
  ListBucketAnalyticsConfigurationsCommandInput,
  ListBucketAnalyticsConfigurationsCommandOutput,
} from "./commands/ListBucketAnalyticsConfigurationsCommand";
import {
  ListBucketIntelligentTieringConfigurationsCommandInput,
  ListBucketIntelligentTieringConfigurationsCommandOutput,
} from "./commands/ListBucketIntelligentTieringConfigurationsCommand";
import {
  ListBucketInventoryConfigurationsCommandInput,
  ListBucketInventoryConfigurationsCommandOutput,
} from "./commands/ListBucketInventoryConfigurationsCommand";
import {
  ListBucketMetricsConfigurationsCommandInput,
  ListBucketMetricsConfigurationsCommandOutput,
} from "./commands/ListBucketMetricsConfigurationsCommand";
import {
  ListBucketsCommandInput,
  ListBucketsCommandOutput,
} from "./commands/ListBucketsCommand";
import {
  ListMultipartUploadsCommandInput,
  ListMultipartUploadsCommandOutput,
} from "./commands/ListMultipartUploadsCommand";
import {
  ListObjectsCommandInput,
  ListObjectsCommandOutput,
} from "./commands/ListObjectsCommand";
import {
  ListObjectsV2CommandInput,
  ListObjectsV2CommandOutput,
} from "./commands/ListObjectsV2Command";
import {
  ListObjectVersionsCommandInput,
  ListObjectVersionsCommandOutput,
} from "./commands/ListObjectVersionsCommand";
import {
  ListPartsCommandInput,
  ListPartsCommandOutput,
} from "./commands/ListPartsCommand";
import {
  PutBucketAccelerateConfigurationCommandInput,
  PutBucketAccelerateConfigurationCommandOutput,
} from "./commands/PutBucketAccelerateConfigurationCommand";
import {
  PutBucketAclCommandInput,
  PutBucketAclCommandOutput,
} from "./commands/PutBucketAclCommand";
import {
  PutBucketAnalyticsConfigurationCommandInput,
  PutBucketAnalyticsConfigurationCommandOutput,
} from "./commands/PutBucketAnalyticsConfigurationCommand";
import {
  PutBucketCorsCommandInput,
  PutBucketCorsCommandOutput,
} from "./commands/PutBucketCorsCommand";
import {
  PutBucketEncryptionCommandInput,
  PutBucketEncryptionCommandOutput,
} from "./commands/PutBucketEncryptionCommand";
import {
  PutBucketIntelligentTieringConfigurationCommandInput,
  PutBucketIntelligentTieringConfigurationCommandOutput,
} from "./commands/PutBucketIntelligentTieringConfigurationCommand";
import {
  PutBucketInventoryConfigurationCommandInput,
  PutBucketInventoryConfigurationCommandOutput,
} from "./commands/PutBucketInventoryConfigurationCommand";
import {
  PutBucketLifecycleConfigurationCommandInput,
  PutBucketLifecycleConfigurationCommandOutput,
} from "./commands/PutBucketLifecycleConfigurationCommand";
import {
  PutBucketLoggingCommandInput,
  PutBucketLoggingCommandOutput,
} from "./commands/PutBucketLoggingCommand";
import {
  PutBucketMetricsConfigurationCommandInput,
  PutBucketMetricsConfigurationCommandOutput,
} from "./commands/PutBucketMetricsConfigurationCommand";
import {
  PutBucketNotificationConfigurationCommandInput,
  PutBucketNotificationConfigurationCommandOutput,
} from "./commands/PutBucketNotificationConfigurationCommand";
import {
  PutBucketOwnershipControlsCommandInput,
  PutBucketOwnershipControlsCommandOutput,
} from "./commands/PutBucketOwnershipControlsCommand";
import {
  PutBucketPolicyCommandInput,
  PutBucketPolicyCommandOutput,
} from "./commands/PutBucketPolicyCommand";
import {
  PutBucketReplicationCommandInput,
  PutBucketReplicationCommandOutput,
} from "./commands/PutBucketReplicationCommand";
import {
  PutBucketRequestPaymentCommandInput,
  PutBucketRequestPaymentCommandOutput,
} from "./commands/PutBucketRequestPaymentCommand";
import {
  PutBucketTaggingCommandInput,
  PutBucketTaggingCommandOutput,
} from "./commands/PutBucketTaggingCommand";
import {
  PutBucketVersioningCommandInput,
  PutBucketVersioningCommandOutput,
} from "./commands/PutBucketVersioningCommand";
import {
  PutBucketWebsiteCommandInput,
  PutBucketWebsiteCommandOutput,
} from "./commands/PutBucketWebsiteCommand";
import {
  PutObjectAclCommandInput,
  PutObjectAclCommandOutput,
} from "./commands/PutObjectAclCommand";
import {
  PutObjectCommandInput,
  PutObjectCommandOutput,
} from "./commands/PutObjectCommand";
import {
  PutObjectLegalHoldCommandInput,
  PutObjectLegalHoldCommandOutput,
} from "./commands/PutObjectLegalHoldCommand";
import {
  PutObjectLockConfigurationCommandInput,
  PutObjectLockConfigurationCommandOutput,
} from "./commands/PutObjectLockConfigurationCommand";
import {
  PutObjectRetentionCommandInput,
  PutObjectRetentionCommandOutput,
} from "./commands/PutObjectRetentionCommand";
import {
  PutObjectTaggingCommandInput,
  PutObjectTaggingCommandOutput,
} from "./commands/PutObjectTaggingCommand";
import {
  PutPublicAccessBlockCommandInput,
  PutPublicAccessBlockCommandOutput,
} from "./commands/PutPublicAccessBlockCommand";
import {
  RestoreObjectCommandInput,
  RestoreObjectCommandOutput,
} from "./commands/RestoreObjectCommand";
import {
  SelectObjectContentCommandInput,
  SelectObjectContentCommandOutput,
} from "./commands/SelectObjectContentCommand";
import {
  UploadPartCommandInput,
  UploadPartCommandOutput,
} from "./commands/UploadPartCommand";
import {
  UploadPartCopyCommandInput,
  UploadPartCopyCommandOutput,
} from "./commands/UploadPartCopyCommand";
import {
  WriteGetObjectResponseCommandInput,
  WriteGetObjectResponseCommandOutput,
} from "./commands/WriteGetObjectResponseCommand";
import { S3Client } from "./S3Client";
export interface S3 {
  abortMultipartUpload(
    args: AbortMultipartUploadCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<AbortMultipartUploadCommandOutput>;
  abortMultipartUpload(
    args: AbortMultipartUploadCommandInput,
    cb: (err: any, data?: AbortMultipartUploadCommandOutput) => void
  ): void;
  abortMultipartUpload(
    args: AbortMultipartUploadCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: AbortMultipartUploadCommandOutput) => void
  ): void;
  completeMultipartUpload(
    args: CompleteMultipartUploadCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CompleteMultipartUploadCommandOutput>;
  completeMultipartUpload(
    args: CompleteMultipartUploadCommandInput,
    cb: (err: any, data?: CompleteMultipartUploadCommandOutput) => void
  ): void;
  completeMultipartUpload(
    args: CompleteMultipartUploadCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CompleteMultipartUploadCommandOutput) => void
  ): void;
  copyObject(
    args: CopyObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CopyObjectCommandOutput>;
  copyObject(
    args: CopyObjectCommandInput,
    cb: (err: any, data?: CopyObjectCommandOutput) => void
  ): void;
  copyObject(
    args: CopyObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CopyObjectCommandOutput) => void
  ): void;
  createBucket(
    args: CreateBucketCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateBucketCommandOutput>;
  createBucket(
    args: CreateBucketCommandInput,
    cb: (err: any, data?: CreateBucketCommandOutput) => void
  ): void;
  createBucket(
    args: CreateBucketCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateBucketCommandOutput) => void
  ): void;
  createMultipartUpload(
    args: CreateMultipartUploadCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<CreateMultipartUploadCommandOutput>;
  createMultipartUpload(
    args: CreateMultipartUploadCommandInput,
    cb: (err: any, data?: CreateMultipartUploadCommandOutput) => void
  ): void;
  createMultipartUpload(
    args: CreateMultipartUploadCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: CreateMultipartUploadCommandOutput) => void
  ): void;
  deleteBucket(
    args: DeleteBucketCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketCommandOutput>;
  deleteBucket(
    args: DeleteBucketCommandInput,
    cb: (err: any, data?: DeleteBucketCommandOutput) => void
  ): void;
  deleteBucket(
    args: DeleteBucketCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketCommandOutput) => void
  ): void;
  deleteBucketAnalyticsConfiguration(
    args: DeleteBucketAnalyticsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketAnalyticsConfigurationCommandOutput>;
  deleteBucketAnalyticsConfiguration(
    args: DeleteBucketAnalyticsConfigurationCommandInput,
    cb: (
      err: any,
      data?: DeleteBucketAnalyticsConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketAnalyticsConfiguration(
    args: DeleteBucketAnalyticsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DeleteBucketAnalyticsConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketCors(
    args: DeleteBucketCorsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketCorsCommandOutput>;
  deleteBucketCors(
    args: DeleteBucketCorsCommandInput,
    cb: (err: any, data?: DeleteBucketCorsCommandOutput) => void
  ): void;
  deleteBucketCors(
    args: DeleteBucketCorsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketCorsCommandOutput) => void
  ): void;
  deleteBucketEncryption(
    args: DeleteBucketEncryptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketEncryptionCommandOutput>;
  deleteBucketEncryption(
    args: DeleteBucketEncryptionCommandInput,
    cb: (err: any, data?: DeleteBucketEncryptionCommandOutput) => void
  ): void;
  deleteBucketEncryption(
    args: DeleteBucketEncryptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketEncryptionCommandOutput) => void
  ): void;
  deleteBucketIntelligentTieringConfiguration(
    args: DeleteBucketIntelligentTieringConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketIntelligentTieringConfigurationCommandOutput>;
  deleteBucketIntelligentTieringConfiguration(
    args: DeleteBucketIntelligentTieringConfigurationCommandInput,
    cb: (
      err: any,
      data?: DeleteBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketIntelligentTieringConfiguration(
    args: DeleteBucketIntelligentTieringConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DeleteBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketInventoryConfiguration(
    args: DeleteBucketInventoryConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketInventoryConfigurationCommandOutput>;
  deleteBucketInventoryConfiguration(
    args: DeleteBucketInventoryConfigurationCommandInput,
    cb: (
      err: any,
      data?: DeleteBucketInventoryConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketInventoryConfiguration(
    args: DeleteBucketInventoryConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: DeleteBucketInventoryConfigurationCommandOutput
    ) => void
  ): void;
  deleteBucketLifecycle(
    args: DeleteBucketLifecycleCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketLifecycleCommandOutput>;
  deleteBucketLifecycle(
    args: DeleteBucketLifecycleCommandInput,
    cb: (err: any, data?: DeleteBucketLifecycleCommandOutput) => void
  ): void;
  deleteBucketLifecycle(
    args: DeleteBucketLifecycleCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketLifecycleCommandOutput) => void
  ): void;
  deleteBucketMetricsConfiguration(
    args: DeleteBucketMetricsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketMetricsConfigurationCommandOutput>;
  deleteBucketMetricsConfiguration(
    args: DeleteBucketMetricsConfigurationCommandInput,
    cb: (err: any, data?: DeleteBucketMetricsConfigurationCommandOutput) => void
  ): void;
  deleteBucketMetricsConfiguration(
    args: DeleteBucketMetricsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketMetricsConfigurationCommandOutput) => void
  ): void;
  deleteBucketOwnershipControls(
    args: DeleteBucketOwnershipControlsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketOwnershipControlsCommandOutput>;
  deleteBucketOwnershipControls(
    args: DeleteBucketOwnershipControlsCommandInput,
    cb: (err: any, data?: DeleteBucketOwnershipControlsCommandOutput) => void
  ): void;
  deleteBucketOwnershipControls(
    args: DeleteBucketOwnershipControlsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketOwnershipControlsCommandOutput) => void
  ): void;
  deleteBucketPolicy(
    args: DeleteBucketPolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketPolicyCommandOutput>;
  deleteBucketPolicy(
    args: DeleteBucketPolicyCommandInput,
    cb: (err: any, data?: DeleteBucketPolicyCommandOutput) => void
  ): void;
  deleteBucketPolicy(
    args: DeleteBucketPolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketPolicyCommandOutput) => void
  ): void;
  deleteBucketReplication(
    args: DeleteBucketReplicationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketReplicationCommandOutput>;
  deleteBucketReplication(
    args: DeleteBucketReplicationCommandInput,
    cb: (err: any, data?: DeleteBucketReplicationCommandOutput) => void
  ): void;
  deleteBucketReplication(
    args: DeleteBucketReplicationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketReplicationCommandOutput) => void
  ): void;
  deleteBucketTagging(
    args: DeleteBucketTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketTaggingCommandOutput>;
  deleteBucketTagging(
    args: DeleteBucketTaggingCommandInput,
    cb: (err: any, data?: DeleteBucketTaggingCommandOutput) => void
  ): void;
  deleteBucketTagging(
    args: DeleteBucketTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketTaggingCommandOutput) => void
  ): void;
  deleteBucketWebsite(
    args: DeleteBucketWebsiteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteBucketWebsiteCommandOutput>;
  deleteBucketWebsite(
    args: DeleteBucketWebsiteCommandInput,
    cb: (err: any, data?: DeleteBucketWebsiteCommandOutput) => void
  ): void;
  deleteBucketWebsite(
    args: DeleteBucketWebsiteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteBucketWebsiteCommandOutput) => void
  ): void;
  deleteObject(
    args: DeleteObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteObjectCommandOutput>;
  deleteObject(
    args: DeleteObjectCommandInput,
    cb: (err: any, data?: DeleteObjectCommandOutput) => void
  ): void;
  deleteObject(
    args: DeleteObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteObjectCommandOutput) => void
  ): void;
  deleteObjects(
    args: DeleteObjectsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteObjectsCommandOutput>;
  deleteObjects(
    args: DeleteObjectsCommandInput,
    cb: (err: any, data?: DeleteObjectsCommandOutput) => void
  ): void;
  deleteObjects(
    args: DeleteObjectsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteObjectsCommandOutput) => void
  ): void;
  deleteObjectTagging(
    args: DeleteObjectTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteObjectTaggingCommandOutput>;
  deleteObjectTagging(
    args: DeleteObjectTaggingCommandInput,
    cb: (err: any, data?: DeleteObjectTaggingCommandOutput) => void
  ): void;
  deleteObjectTagging(
    args: DeleteObjectTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteObjectTaggingCommandOutput) => void
  ): void;
  deletePublicAccessBlock(
    args: DeletePublicAccessBlockCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeletePublicAccessBlockCommandOutput>;
  deletePublicAccessBlock(
    args: DeletePublicAccessBlockCommandInput,
    cb: (err: any, data?: DeletePublicAccessBlockCommandOutput) => void
  ): void;
  deletePublicAccessBlock(
    args: DeletePublicAccessBlockCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeletePublicAccessBlockCommandOutput) => void
  ): void;
  getBucketAccelerateConfiguration(
    args: GetBucketAccelerateConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketAccelerateConfigurationCommandOutput>;
  getBucketAccelerateConfiguration(
    args: GetBucketAccelerateConfigurationCommandInput,
    cb: (err: any, data?: GetBucketAccelerateConfigurationCommandOutput) => void
  ): void;
  getBucketAccelerateConfiguration(
    args: GetBucketAccelerateConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketAccelerateConfigurationCommandOutput) => void
  ): void;
  getBucketAcl(
    args: GetBucketAclCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketAclCommandOutput>;
  getBucketAcl(
    args: GetBucketAclCommandInput,
    cb: (err: any, data?: GetBucketAclCommandOutput) => void
  ): void;
  getBucketAcl(
    args: GetBucketAclCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketAclCommandOutput) => void
  ): void;
  getBucketAnalyticsConfiguration(
    args: GetBucketAnalyticsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketAnalyticsConfigurationCommandOutput>;
  getBucketAnalyticsConfiguration(
    args: GetBucketAnalyticsConfigurationCommandInput,
    cb: (err: any, data?: GetBucketAnalyticsConfigurationCommandOutput) => void
  ): void;
  getBucketAnalyticsConfiguration(
    args: GetBucketAnalyticsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketAnalyticsConfigurationCommandOutput) => void
  ): void;
  getBucketCors(
    args: GetBucketCorsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketCorsCommandOutput>;
  getBucketCors(
    args: GetBucketCorsCommandInput,
    cb: (err: any, data?: GetBucketCorsCommandOutput) => void
  ): void;
  getBucketCors(
    args: GetBucketCorsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketCorsCommandOutput) => void
  ): void;
  getBucketEncryption(
    args: GetBucketEncryptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketEncryptionCommandOutput>;
  getBucketEncryption(
    args: GetBucketEncryptionCommandInput,
    cb: (err: any, data?: GetBucketEncryptionCommandOutput) => void
  ): void;
  getBucketEncryption(
    args: GetBucketEncryptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketEncryptionCommandOutput) => void
  ): void;
  getBucketIntelligentTieringConfiguration(
    args: GetBucketIntelligentTieringConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketIntelligentTieringConfigurationCommandOutput>;
  getBucketIntelligentTieringConfiguration(
    args: GetBucketIntelligentTieringConfigurationCommandInput,
    cb: (
      err: any,
      data?: GetBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  getBucketIntelligentTieringConfiguration(
    args: GetBucketIntelligentTieringConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: GetBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  getBucketInventoryConfiguration(
    args: GetBucketInventoryConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketInventoryConfigurationCommandOutput>;
  getBucketInventoryConfiguration(
    args: GetBucketInventoryConfigurationCommandInput,
    cb: (err: any, data?: GetBucketInventoryConfigurationCommandOutput) => void
  ): void;
  getBucketInventoryConfiguration(
    args: GetBucketInventoryConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketInventoryConfigurationCommandOutput) => void
  ): void;
  getBucketLifecycleConfiguration(
    args: GetBucketLifecycleConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketLifecycleConfigurationCommandOutput>;
  getBucketLifecycleConfiguration(
    args: GetBucketLifecycleConfigurationCommandInput,
    cb: (err: any, data?: GetBucketLifecycleConfigurationCommandOutput) => void
  ): void;
  getBucketLifecycleConfiguration(
    args: GetBucketLifecycleConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketLifecycleConfigurationCommandOutput) => void
  ): void;
  getBucketLocation(
    args: GetBucketLocationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketLocationCommandOutput>;
  getBucketLocation(
    args: GetBucketLocationCommandInput,
    cb: (err: any, data?: GetBucketLocationCommandOutput) => void
  ): void;
  getBucketLocation(
    args: GetBucketLocationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketLocationCommandOutput) => void
  ): void;
  getBucketLogging(
    args: GetBucketLoggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketLoggingCommandOutput>;
  getBucketLogging(
    args: GetBucketLoggingCommandInput,
    cb: (err: any, data?: GetBucketLoggingCommandOutput) => void
  ): void;
  getBucketLogging(
    args: GetBucketLoggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketLoggingCommandOutput) => void
  ): void;
  getBucketMetricsConfiguration(
    args: GetBucketMetricsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketMetricsConfigurationCommandOutput>;
  getBucketMetricsConfiguration(
    args: GetBucketMetricsConfigurationCommandInput,
    cb: (err: any, data?: GetBucketMetricsConfigurationCommandOutput) => void
  ): void;
  getBucketMetricsConfiguration(
    args: GetBucketMetricsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketMetricsConfigurationCommandOutput) => void
  ): void;
  getBucketNotificationConfiguration(
    args: GetBucketNotificationConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketNotificationConfigurationCommandOutput>;
  getBucketNotificationConfiguration(
    args: GetBucketNotificationConfigurationCommandInput,
    cb: (
      err: any,
      data?: GetBucketNotificationConfigurationCommandOutput
    ) => void
  ): void;
  getBucketNotificationConfiguration(
    args: GetBucketNotificationConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: GetBucketNotificationConfigurationCommandOutput
    ) => void
  ): void;
  getBucketOwnershipControls(
    args: GetBucketOwnershipControlsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketOwnershipControlsCommandOutput>;
  getBucketOwnershipControls(
    args: GetBucketOwnershipControlsCommandInput,
    cb: (err: any, data?: GetBucketOwnershipControlsCommandOutput) => void
  ): void;
  getBucketOwnershipControls(
    args: GetBucketOwnershipControlsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketOwnershipControlsCommandOutput) => void
  ): void;
  getBucketPolicy(
    args: GetBucketPolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketPolicyCommandOutput>;
  getBucketPolicy(
    args: GetBucketPolicyCommandInput,
    cb: (err: any, data?: GetBucketPolicyCommandOutput) => void
  ): void;
  getBucketPolicy(
    args: GetBucketPolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketPolicyCommandOutput) => void
  ): void;
  getBucketPolicyStatus(
    args: GetBucketPolicyStatusCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketPolicyStatusCommandOutput>;
  getBucketPolicyStatus(
    args: GetBucketPolicyStatusCommandInput,
    cb: (err: any, data?: GetBucketPolicyStatusCommandOutput) => void
  ): void;
  getBucketPolicyStatus(
    args: GetBucketPolicyStatusCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketPolicyStatusCommandOutput) => void
  ): void;
  getBucketReplication(
    args: GetBucketReplicationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketReplicationCommandOutput>;
  getBucketReplication(
    args: GetBucketReplicationCommandInput,
    cb: (err: any, data?: GetBucketReplicationCommandOutput) => void
  ): void;
  getBucketReplication(
    args: GetBucketReplicationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketReplicationCommandOutput) => void
  ): void;
  getBucketRequestPayment(
    args: GetBucketRequestPaymentCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketRequestPaymentCommandOutput>;
  getBucketRequestPayment(
    args: GetBucketRequestPaymentCommandInput,
    cb: (err: any, data?: GetBucketRequestPaymentCommandOutput) => void
  ): void;
  getBucketRequestPayment(
    args: GetBucketRequestPaymentCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketRequestPaymentCommandOutput) => void
  ): void;
  getBucketTagging(
    args: GetBucketTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketTaggingCommandOutput>;
  getBucketTagging(
    args: GetBucketTaggingCommandInput,
    cb: (err: any, data?: GetBucketTaggingCommandOutput) => void
  ): void;
  getBucketTagging(
    args: GetBucketTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketTaggingCommandOutput) => void
  ): void;
  getBucketVersioning(
    args: GetBucketVersioningCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketVersioningCommandOutput>;
  getBucketVersioning(
    args: GetBucketVersioningCommandInput,
    cb: (err: any, data?: GetBucketVersioningCommandOutput) => void
  ): void;
  getBucketVersioning(
    args: GetBucketVersioningCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketVersioningCommandOutput) => void
  ): void;
  getBucketWebsite(
    args: GetBucketWebsiteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetBucketWebsiteCommandOutput>;
  getBucketWebsite(
    args: GetBucketWebsiteCommandInput,
    cb: (err: any, data?: GetBucketWebsiteCommandOutput) => void
  ): void;
  getBucketWebsite(
    args: GetBucketWebsiteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetBucketWebsiteCommandOutput) => void
  ): void;
  getObject(
    args: GetObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectCommandOutput>;
  getObject(
    args: GetObjectCommandInput,
    cb: (err: any, data?: GetObjectCommandOutput) => void
  ): void;
  getObject(
    args: GetObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectCommandOutput) => void
  ): void;
  getObjectAcl(
    args: GetObjectAclCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectAclCommandOutput>;
  getObjectAcl(
    args: GetObjectAclCommandInput,
    cb: (err: any, data?: GetObjectAclCommandOutput) => void
  ): void;
  getObjectAcl(
    args: GetObjectAclCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectAclCommandOutput) => void
  ): void;
  getObjectAttributes(
    args: GetObjectAttributesCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectAttributesCommandOutput>;
  getObjectAttributes(
    args: GetObjectAttributesCommandInput,
    cb: (err: any, data?: GetObjectAttributesCommandOutput) => void
  ): void;
  getObjectAttributes(
    args: GetObjectAttributesCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectAttributesCommandOutput) => void
  ): void;
  getObjectLegalHold(
    args: GetObjectLegalHoldCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectLegalHoldCommandOutput>;
  getObjectLegalHold(
    args: GetObjectLegalHoldCommandInput,
    cb: (err: any, data?: GetObjectLegalHoldCommandOutput) => void
  ): void;
  getObjectLegalHold(
    args: GetObjectLegalHoldCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectLegalHoldCommandOutput) => void
  ): void;
  getObjectLockConfiguration(
    args: GetObjectLockConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectLockConfigurationCommandOutput>;
  getObjectLockConfiguration(
    args: GetObjectLockConfigurationCommandInput,
    cb: (err: any, data?: GetObjectLockConfigurationCommandOutput) => void
  ): void;
  getObjectLockConfiguration(
    args: GetObjectLockConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectLockConfigurationCommandOutput) => void
  ): void;
  getObjectRetention(
    args: GetObjectRetentionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectRetentionCommandOutput>;
  getObjectRetention(
    args: GetObjectRetentionCommandInput,
    cb: (err: any, data?: GetObjectRetentionCommandOutput) => void
  ): void;
  getObjectRetention(
    args: GetObjectRetentionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectRetentionCommandOutput) => void
  ): void;
  getObjectTagging(
    args: GetObjectTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectTaggingCommandOutput>;
  getObjectTagging(
    args: GetObjectTaggingCommandInput,
    cb: (err: any, data?: GetObjectTaggingCommandOutput) => void
  ): void;
  getObjectTagging(
    args: GetObjectTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectTaggingCommandOutput) => void
  ): void;
  getObjectTorrent(
    args: GetObjectTorrentCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetObjectTorrentCommandOutput>;
  getObjectTorrent(
    args: GetObjectTorrentCommandInput,
    cb: (err: any, data?: GetObjectTorrentCommandOutput) => void
  ): void;
  getObjectTorrent(
    args: GetObjectTorrentCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetObjectTorrentCommandOutput) => void
  ): void;
  getPublicAccessBlock(
    args: GetPublicAccessBlockCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetPublicAccessBlockCommandOutput>;
  getPublicAccessBlock(
    args: GetPublicAccessBlockCommandInput,
    cb: (err: any, data?: GetPublicAccessBlockCommandOutput) => void
  ): void;
  getPublicAccessBlock(
    args: GetPublicAccessBlockCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetPublicAccessBlockCommandOutput) => void
  ): void;
  headBucket(
    args: HeadBucketCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<HeadBucketCommandOutput>;
  headBucket(
    args: HeadBucketCommandInput,
    cb: (err: any, data?: HeadBucketCommandOutput) => void
  ): void;
  headBucket(
    args: HeadBucketCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: HeadBucketCommandOutput) => void
  ): void;
  headObject(
    args: HeadObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<HeadObjectCommandOutput>;
  headObject(
    args: HeadObjectCommandInput,
    cb: (err: any, data?: HeadObjectCommandOutput) => void
  ): void;
  headObject(
    args: HeadObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: HeadObjectCommandOutput) => void
  ): void;
  listBucketAnalyticsConfigurations(
    args: ListBucketAnalyticsConfigurationsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBucketAnalyticsConfigurationsCommandOutput>;
  listBucketAnalyticsConfigurations(
    args: ListBucketAnalyticsConfigurationsCommandInput,
    cb: (
      err: any,
      data?: ListBucketAnalyticsConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketAnalyticsConfigurations(
    args: ListBucketAnalyticsConfigurationsCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: ListBucketAnalyticsConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketIntelligentTieringConfigurations(
    args: ListBucketIntelligentTieringConfigurationsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBucketIntelligentTieringConfigurationsCommandOutput>;
  listBucketIntelligentTieringConfigurations(
    args: ListBucketIntelligentTieringConfigurationsCommandInput,
    cb: (
      err: any,
      data?: ListBucketIntelligentTieringConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketIntelligentTieringConfigurations(
    args: ListBucketIntelligentTieringConfigurationsCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: ListBucketIntelligentTieringConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketInventoryConfigurations(
    args: ListBucketInventoryConfigurationsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBucketInventoryConfigurationsCommandOutput>;
  listBucketInventoryConfigurations(
    args: ListBucketInventoryConfigurationsCommandInput,
    cb: (
      err: any,
      data?: ListBucketInventoryConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketInventoryConfigurations(
    args: ListBucketInventoryConfigurationsCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: ListBucketInventoryConfigurationsCommandOutput
    ) => void
  ): void;
  listBucketMetricsConfigurations(
    args: ListBucketMetricsConfigurationsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBucketMetricsConfigurationsCommandOutput>;
  listBucketMetricsConfigurations(
    args: ListBucketMetricsConfigurationsCommandInput,
    cb: (err: any, data?: ListBucketMetricsConfigurationsCommandOutput) => void
  ): void;
  listBucketMetricsConfigurations(
    args: ListBucketMetricsConfigurationsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListBucketMetricsConfigurationsCommandOutput) => void
  ): void;
  listBuckets(
    args: ListBucketsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListBucketsCommandOutput>;
  listBuckets(
    args: ListBucketsCommandInput,
    cb: (err: any, data?: ListBucketsCommandOutput) => void
  ): void;
  listBuckets(
    args: ListBucketsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListBucketsCommandOutput) => void
  ): void;
  listMultipartUploads(
    args: ListMultipartUploadsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListMultipartUploadsCommandOutput>;
  listMultipartUploads(
    args: ListMultipartUploadsCommandInput,
    cb: (err: any, data?: ListMultipartUploadsCommandOutput) => void
  ): void;
  listMultipartUploads(
    args: ListMultipartUploadsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListMultipartUploadsCommandOutput) => void
  ): void;
  listObjects(
    args: ListObjectsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListObjectsCommandOutput>;
  listObjects(
    args: ListObjectsCommandInput,
    cb: (err: any, data?: ListObjectsCommandOutput) => void
  ): void;
  listObjects(
    args: ListObjectsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListObjectsCommandOutput) => void
  ): void;
  listObjectsV2(
    args: ListObjectsV2CommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListObjectsV2CommandOutput>;
  listObjectsV2(
    args: ListObjectsV2CommandInput,
    cb: (err: any, data?: ListObjectsV2CommandOutput) => void
  ): void;
  listObjectsV2(
    args: ListObjectsV2CommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListObjectsV2CommandOutput) => void
  ): void;
  listObjectVersions(
    args: ListObjectVersionsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListObjectVersionsCommandOutput>;
  listObjectVersions(
    args: ListObjectVersionsCommandInput,
    cb: (err: any, data?: ListObjectVersionsCommandOutput) => void
  ): void;
  listObjectVersions(
    args: ListObjectVersionsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListObjectVersionsCommandOutput) => void
  ): void;
  listParts(
    args: ListPartsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<ListPartsCommandOutput>;
  listParts(
    args: ListPartsCommandInput,
    cb: (err: any, data?: ListPartsCommandOutput) => void
  ): void;
  listParts(
    args: ListPartsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: ListPartsCommandOutput) => void
  ): void;
  putBucketAccelerateConfiguration(
    args: PutBucketAccelerateConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketAccelerateConfigurationCommandOutput>;
  putBucketAccelerateConfiguration(
    args: PutBucketAccelerateConfigurationCommandInput,
    cb: (err: any, data?: PutBucketAccelerateConfigurationCommandOutput) => void
  ): void;
  putBucketAccelerateConfiguration(
    args: PutBucketAccelerateConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketAccelerateConfigurationCommandOutput) => void
  ): void;
  putBucketAcl(
    args: PutBucketAclCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketAclCommandOutput>;
  putBucketAcl(
    args: PutBucketAclCommandInput,
    cb: (err: any, data?: PutBucketAclCommandOutput) => void
  ): void;
  putBucketAcl(
    args: PutBucketAclCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketAclCommandOutput) => void
  ): void;
  putBucketAnalyticsConfiguration(
    args: PutBucketAnalyticsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketAnalyticsConfigurationCommandOutput>;
  putBucketAnalyticsConfiguration(
    args: PutBucketAnalyticsConfigurationCommandInput,
    cb: (err: any, data?: PutBucketAnalyticsConfigurationCommandOutput) => void
  ): void;
  putBucketAnalyticsConfiguration(
    args: PutBucketAnalyticsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketAnalyticsConfigurationCommandOutput) => void
  ): void;
  putBucketCors(
    args: PutBucketCorsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketCorsCommandOutput>;
  putBucketCors(
    args: PutBucketCorsCommandInput,
    cb: (err: any, data?: PutBucketCorsCommandOutput) => void
  ): void;
  putBucketCors(
    args: PutBucketCorsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketCorsCommandOutput) => void
  ): void;
  putBucketEncryption(
    args: PutBucketEncryptionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketEncryptionCommandOutput>;
  putBucketEncryption(
    args: PutBucketEncryptionCommandInput,
    cb: (err: any, data?: PutBucketEncryptionCommandOutput) => void
  ): void;
  putBucketEncryption(
    args: PutBucketEncryptionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketEncryptionCommandOutput) => void
  ): void;
  putBucketIntelligentTieringConfiguration(
    args: PutBucketIntelligentTieringConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketIntelligentTieringConfigurationCommandOutput>;
  putBucketIntelligentTieringConfiguration(
    args: PutBucketIntelligentTieringConfigurationCommandInput,
    cb: (
      err: any,
      data?: PutBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  putBucketIntelligentTieringConfiguration(
    args: PutBucketIntelligentTieringConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: PutBucketIntelligentTieringConfigurationCommandOutput
    ) => void
  ): void;
  putBucketInventoryConfiguration(
    args: PutBucketInventoryConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketInventoryConfigurationCommandOutput>;
  putBucketInventoryConfiguration(
    args: PutBucketInventoryConfigurationCommandInput,
    cb: (err: any, data?: PutBucketInventoryConfigurationCommandOutput) => void
  ): void;
  putBucketInventoryConfiguration(
    args: PutBucketInventoryConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketInventoryConfigurationCommandOutput) => void
  ): void;
  putBucketLifecycleConfiguration(
    args: PutBucketLifecycleConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketLifecycleConfigurationCommandOutput>;
  putBucketLifecycleConfiguration(
    args: PutBucketLifecycleConfigurationCommandInput,
    cb: (err: any, data?: PutBucketLifecycleConfigurationCommandOutput) => void
  ): void;
  putBucketLifecycleConfiguration(
    args: PutBucketLifecycleConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketLifecycleConfigurationCommandOutput) => void
  ): void;
  putBucketLogging(
    args: PutBucketLoggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketLoggingCommandOutput>;
  putBucketLogging(
    args: PutBucketLoggingCommandInput,
    cb: (err: any, data?: PutBucketLoggingCommandOutput) => void
  ): void;
  putBucketLogging(
    args: PutBucketLoggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketLoggingCommandOutput) => void
  ): void;
  putBucketMetricsConfiguration(
    args: PutBucketMetricsConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketMetricsConfigurationCommandOutput>;
  putBucketMetricsConfiguration(
    args: PutBucketMetricsConfigurationCommandInput,
    cb: (err: any, data?: PutBucketMetricsConfigurationCommandOutput) => void
  ): void;
  putBucketMetricsConfiguration(
    args: PutBucketMetricsConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketMetricsConfigurationCommandOutput) => void
  ): void;
  putBucketNotificationConfiguration(
    args: PutBucketNotificationConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketNotificationConfigurationCommandOutput>;
  putBucketNotificationConfiguration(
    args: PutBucketNotificationConfigurationCommandInput,
    cb: (
      err: any,
      data?: PutBucketNotificationConfigurationCommandOutput
    ) => void
  ): void;
  putBucketNotificationConfiguration(
    args: PutBucketNotificationConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (
      err: any,
      data?: PutBucketNotificationConfigurationCommandOutput
    ) => void
  ): void;
  putBucketOwnershipControls(
    args: PutBucketOwnershipControlsCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketOwnershipControlsCommandOutput>;
  putBucketOwnershipControls(
    args: PutBucketOwnershipControlsCommandInput,
    cb: (err: any, data?: PutBucketOwnershipControlsCommandOutput) => void
  ): void;
  putBucketOwnershipControls(
    args: PutBucketOwnershipControlsCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketOwnershipControlsCommandOutput) => void
  ): void;
  putBucketPolicy(
    args: PutBucketPolicyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketPolicyCommandOutput>;
  putBucketPolicy(
    args: PutBucketPolicyCommandInput,
    cb: (err: any, data?: PutBucketPolicyCommandOutput) => void
  ): void;
  putBucketPolicy(
    args: PutBucketPolicyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketPolicyCommandOutput) => void
  ): void;
  putBucketReplication(
    args: PutBucketReplicationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketReplicationCommandOutput>;
  putBucketReplication(
    args: PutBucketReplicationCommandInput,
    cb: (err: any, data?: PutBucketReplicationCommandOutput) => void
  ): void;
  putBucketReplication(
    args: PutBucketReplicationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketReplicationCommandOutput) => void
  ): void;
  putBucketRequestPayment(
    args: PutBucketRequestPaymentCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketRequestPaymentCommandOutput>;
  putBucketRequestPayment(
    args: PutBucketRequestPaymentCommandInput,
    cb: (err: any, data?: PutBucketRequestPaymentCommandOutput) => void
  ): void;
  putBucketRequestPayment(
    args: PutBucketRequestPaymentCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketRequestPaymentCommandOutput) => void
  ): void;
  putBucketTagging(
    args: PutBucketTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketTaggingCommandOutput>;
  putBucketTagging(
    args: PutBucketTaggingCommandInput,
    cb: (err: any, data?: PutBucketTaggingCommandOutput) => void
  ): void;
  putBucketTagging(
    args: PutBucketTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketTaggingCommandOutput) => void
  ): void;
  putBucketVersioning(
    args: PutBucketVersioningCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketVersioningCommandOutput>;
  putBucketVersioning(
    args: PutBucketVersioningCommandInput,
    cb: (err: any, data?: PutBucketVersioningCommandOutput) => void
  ): void;
  putBucketVersioning(
    args: PutBucketVersioningCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketVersioningCommandOutput) => void
  ): void;
  putBucketWebsite(
    args: PutBucketWebsiteCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutBucketWebsiteCommandOutput>;
  putBucketWebsite(
    args: PutBucketWebsiteCommandInput,
    cb: (err: any, data?: PutBucketWebsiteCommandOutput) => void
  ): void;
  putBucketWebsite(
    args: PutBucketWebsiteCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutBucketWebsiteCommandOutput) => void
  ): void;
  putObject(
    args: PutObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectCommandOutput>;
  putObject(
    args: PutObjectCommandInput,
    cb: (err: any, data?: PutObjectCommandOutput) => void
  ): void;
  putObject(
    args: PutObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectCommandOutput) => void
  ): void;
  putObjectAcl(
    args: PutObjectAclCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectAclCommandOutput>;
  putObjectAcl(
    args: PutObjectAclCommandInput,
    cb: (err: any, data?: PutObjectAclCommandOutput) => void
  ): void;
  putObjectAcl(
    args: PutObjectAclCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectAclCommandOutput) => void
  ): void;
  putObjectLegalHold(
    args: PutObjectLegalHoldCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectLegalHoldCommandOutput>;
  putObjectLegalHold(
    args: PutObjectLegalHoldCommandInput,
    cb: (err: any, data?: PutObjectLegalHoldCommandOutput) => void
  ): void;
  putObjectLegalHold(
    args: PutObjectLegalHoldCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectLegalHoldCommandOutput) => void
  ): void;
  putObjectLockConfiguration(
    args: PutObjectLockConfigurationCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectLockConfigurationCommandOutput>;
  putObjectLockConfiguration(
    args: PutObjectLockConfigurationCommandInput,
    cb: (err: any, data?: PutObjectLockConfigurationCommandOutput) => void
  ): void;
  putObjectLockConfiguration(
    args: PutObjectLockConfigurationCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectLockConfigurationCommandOutput) => void
  ): void;
  putObjectRetention(
    args: PutObjectRetentionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectRetentionCommandOutput>;
  putObjectRetention(
    args: PutObjectRetentionCommandInput,
    cb: (err: any, data?: PutObjectRetentionCommandOutput) => void
  ): void;
  putObjectRetention(
    args: PutObjectRetentionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectRetentionCommandOutput) => void
  ): void;
  putObjectTagging(
    args: PutObjectTaggingCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutObjectTaggingCommandOutput>;
  putObjectTagging(
    args: PutObjectTaggingCommandInput,
    cb: (err: any, data?: PutObjectTaggingCommandOutput) => void
  ): void;
  putObjectTagging(
    args: PutObjectTaggingCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutObjectTaggingCommandOutput) => void
  ): void;
  putPublicAccessBlock(
    args: PutPublicAccessBlockCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PutPublicAccessBlockCommandOutput>;
  putPublicAccessBlock(
    args: PutPublicAccessBlockCommandInput,
    cb: (err: any, data?: PutPublicAccessBlockCommandOutput) => void
  ): void;
  putPublicAccessBlock(
    args: PutPublicAccessBlockCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PutPublicAccessBlockCommandOutput) => void
  ): void;
  restoreObject(
    args: RestoreObjectCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<RestoreObjectCommandOutput>;
  restoreObject(
    args: RestoreObjectCommandInput,
    cb: (err: any, data?: RestoreObjectCommandOutput) => void
  ): void;
  restoreObject(
    args: RestoreObjectCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: RestoreObjectCommandOutput) => void
  ): void;
  selectObjectContent(
    args: SelectObjectContentCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<SelectObjectContentCommandOutput>;
  selectObjectContent(
    args: SelectObjectContentCommandInput,
    cb: (err: any, data?: SelectObjectContentCommandOutput) => void
  ): void;
  selectObjectContent(
    args: SelectObjectContentCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: SelectObjectContentCommandOutput) => void
  ): void;
  uploadPart(
    args: UploadPartCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UploadPartCommandOutput>;
  uploadPart(
    args: UploadPartCommandInput,
    cb: (err: any, data?: UploadPartCommandOutput) => void
  ): void;
  uploadPart(
    args: UploadPartCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UploadPartCommandOutput) => void
  ): void;
  uploadPartCopy(
    args: UploadPartCopyCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<UploadPartCopyCommandOutput>;
  uploadPartCopy(
    args: UploadPartCopyCommandInput,
    cb: (err: any, data?: UploadPartCopyCommandOutput) => void
  ): void;
  uploadPartCopy(
    args: UploadPartCopyCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: UploadPartCopyCommandOutput) => void
  ): void;
  writeGetObjectResponse(
    args: WriteGetObjectResponseCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<WriteGetObjectResponseCommandOutput>;
  writeGetObjectResponse(
    args: WriteGetObjectResponseCommandInput,
    cb: (err: any, data?: WriteGetObjectResponseCommandOutput) => void
  ): void;
  writeGetObjectResponse(
    args: WriteGetObjectResponseCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: WriteGetObjectResponseCommandOutput) => void
  ): void;
}
export declare class S3 extends S3Client implements S3 {}
