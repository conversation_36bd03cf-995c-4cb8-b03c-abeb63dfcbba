// ملف مركزي يحتوي على العقارات وأسعارها والأرباح والموارد المطلوبة
const properties = [
  { name: 'شركة اتصالات', price: 5000000, profit: 25000, emoji: '📡', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'شركة تقنية', price: 7500000, profit: 35000, emoji: '💻', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'مستشفى', price: 10000000, profit: 50000, emoji: '🏥', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'برج', price: 15000000, profit: 75000, emoji: '🏢', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'محطة وقود', price: 3000000, profit: 15000, emoji: '⛽', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'سوبر ماركت', price: 2000000, profit: 10000, emoji: '🏪', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'مقهى', price: 1500000, profit: 7500, emoji: '☕', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'مطعم', price: 2500000, profit: 12500, emoji: '🍽️', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'صالون', price: 1000000, profit: 5000, emoji: '💇', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'محل ملابس', price: 1200000, profit: 6000, emoji: '👕', resources: { wood: 300000, brick: 240000, stone: 180000 } },
  { name: 'مكتبة', price: 800000, profit: 4000, emoji: '📚', resources: { wood: 300000, brick: 240000, stone: 180000 } }
];

module.exports = properties;
