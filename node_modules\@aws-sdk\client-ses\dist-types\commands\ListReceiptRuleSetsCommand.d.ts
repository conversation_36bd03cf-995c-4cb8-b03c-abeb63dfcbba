import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { ListReceiptRuleSetsRequest, ListReceiptRuleSetsResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link ListReceiptRuleSetsCommand}.
 */
export interface ListReceiptRuleSetsCommandInput extends ListReceiptRuleSetsRequest {
}
/**
 * @public
 *
 * The output of {@link ListReceiptRuleSetsCommand}.
 */
export interface ListReceiptRuleSetsCommandOutput extends ListReceiptRuleSetsResponse, __<PERSON>ada<PERSON>earer {
}
/**
 * @public
 * <p>Lists the receipt rule sets that exist under your AWS account in the current AWS
 *             Region. If there are additional receipt rule sets to be retrieved, you will receive a
 *                 <code>NextToken</code> that you can provide to the next call to
 *                 <code>ListReceiptRuleSets</code> to retrieve the additional entries.</p>
 *         <p>For information about managing receipt rule sets, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email-managing-receipt-rule-sets.html">Amazon SES Developer Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, ListReceiptRuleSetsCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, ListReceiptRuleSetsCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // ListReceiptRuleSetsRequest
 *   NextToken: "STRING_VALUE",
 * };
 * const command = new ListReceiptRuleSetsCommand(input);
 * const response = await client.send(command);
 * // { // ListReceiptRuleSetsResponse
 * //   RuleSets: [ // ReceiptRuleSetsLists
 * //     { // ReceiptRuleSetMetadata
 * //       Name: "STRING_VALUE",
 * //       CreatedTimestamp: new Date("TIMESTAMP"),
 * //     },
 * //   ],
 * //   NextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListReceiptRuleSetsCommandInput - {@link ListReceiptRuleSetsCommandInput}
 * @returns {@link ListReceiptRuleSetsCommandOutput}
 * @see {@link ListReceiptRuleSetsCommandInput} for command's `input` shape.
 * @see {@link ListReceiptRuleSetsCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example ListReceiptRuleSets
 * ```javascript
 * // The following example lists the receipt rule sets that exist under an AWS account:
 * const input = {
 *   "NextToken": ""
 * };
 * const command = new ListReceiptRuleSetsCommand(input);
 * const response = await client.send(command);
 * /* response ==
 * {
 *   "NextToken": "",
 *   "RuleSets": [
 *     {
 *       "CreatedTimestamp": "2016-07-15T16:25:59.607Z",
 *       "Name": "MyRuleSet"
 *     }
 *   ]
 * }
 * *\/
 * // example id: listreceiptrulesets-*************
 * ```
 *
 */
export declare class ListReceiptRuleSetsCommand extends $Command<ListReceiptRuleSetsCommandInput, ListReceiptRuleSetsCommandOutput, SESClientResolvedConfig> {
    readonly input: ListReceiptRuleSetsCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: ListReceiptRuleSetsCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<ListReceiptRuleSetsCommandInput, ListReceiptRuleSetsCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
