import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __Metada<PERSON>Bearer, MiddlewareStack } from "@aws-sdk/types";
import { SetIdentityDkimEnabledRequest, SetIdentityDkimEnabledResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link SetIdentityDkimEnabledCommand}.
 */
export interface SetIdentityDkimEnabledCommandInput extends SetIdentityDkimEnabledRequest {
}
/**
 * @public
 *
 * The output of {@link SetIdentityDkimEnabledCommand}.
 */
export interface SetIdentityDkimEnabledCommandOutput extends SetIdentityDkimEnabledResponse, __<PERSON>ada<PERSON>earer {
}
/**
 * @public
 * <p>Enables or disables Easy DKIM signing of email sent from an identity. If Easy DKIM
 *             signing is enabled for a domain, then Amazon SES uses DKIM to sign all email that it sends
 *             from addresses on that domain. If Easy DKIM signing is enabled for an email address,
 *             then Amazon SES uses DKIM to sign all email it sends from that address.</p>
 *         <note>
 *             <p>For email addresses (for example, <code><EMAIL></code>), you can only
 *                 enable DKIM signing if the corresponding domain (in this case,
 *                     <code>example.com</code>) has been set up to use Easy DKIM.</p>
 *         </note>
 *         <p>You can enable DKIM signing for an identity at any time after you start the
 *             verification process for the identity, even if the verification process isn't complete. </p>
 *         <p>You can execute this operation no more than once per second.</p>
 *         <p>For more information about Easy DKIM signing, go to the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/easy-dkim.html">Amazon SES Developer Guide</a>.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, SetIdentityDkimEnabledCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, SetIdentityDkimEnabledCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // SetIdentityDkimEnabledRequest
 *   Identity: "STRING_VALUE", // required
 *   DkimEnabled: true || false, // required
 * };
 * const command = new SetIdentityDkimEnabledCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param SetIdentityDkimEnabledCommandInput - {@link SetIdentityDkimEnabledCommandInput}
 * @returns {@link SetIdentityDkimEnabledCommandOutput}
 * @see {@link SetIdentityDkimEnabledCommandInput} for command's `input` shape.
 * @see {@link SetIdentityDkimEnabledCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example SetIdentityDkimEnabled
 * ```javascript
 * // The following example configures Amazon SES to Easy DKIM-sign the email sent from an identity:
 * const input = {
 *   "DkimEnabled": true,
 *   "Identity": "<EMAIL>"
 * };
 * const command = new SetIdentityDkimEnabledCommand(input);
 * await client.send(command);
 * // example id: setidentitydkimenabled-1469057485202
 * ```
 *
 */
export declare class SetIdentityDkimEnabledCommand extends $Command<SetIdentityDkimEnabledCommandInput, SetIdentityDkimEnabledCommandOutput, SESClientResolvedConfig> {
    readonly input: SetIdentityDkimEnabledCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: SetIdentityDkimEnabledCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<SetIdentityDkimEnabledCommandInput, SetIdentityDkimEnabledCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
