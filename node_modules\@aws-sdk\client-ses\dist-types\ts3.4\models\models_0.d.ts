import { ExceptionOptionType as __ExceptionOptionType } from "@aws-sdk/smithy-client";
import { SESServiceException as __BaseException } from "./SESServiceException";
export declare class AccountSendingPausedException extends __BaseException {
  readonly name: "AccountSendingPausedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<AccountSendingPausedException, __BaseException>
  );
}
export interface AddHeaderAction {
  HeaderName: string | undefined;
  HeaderValue: string | undefined;
}
export declare class AlreadyExistsException extends __BaseException {
  readonly name: "AlreadyExistsException";
  readonly $fault: "client";
  Name?: string;
  constructor(
    opts: __ExceptionOptionType<AlreadyExistsException, __BaseException>
  );
}
export declare const BehaviorOnMXFailure: {
  readonly RejectMessage: "RejectMessage";
  readonly UseDefaultValue: "UseDefaultValue";
};
export type BehaviorOnMXFailure =
  (typeof BehaviorOnMXFailure)[keyof typeof BehaviorOnMXFailure];
export interface Content {
  Data: string | undefined;
  Charset?: string;
}
export interface Body {
  Text?: Content;
  Html?: Content;
}
export interface BounceAction {
  TopicArn?: string;
  SmtpReplyCode: string | undefined;
  StatusCode?: string;
  Message: string | undefined;
  Sender: string | undefined;
}
export declare const BounceType: {
  readonly ContentRejected: "ContentRejected";
  readonly DoesNotExist: "DoesNotExist";
  readonly ExceededQuota: "ExceededQuota";
  readonly MessageTooLarge: "MessageTooLarge";
  readonly TemporaryFailure: "TemporaryFailure";
  readonly Undefined: "Undefined";
};
export type BounceType = (typeof BounceType)[keyof typeof BounceType];
export declare const DsnAction: {
  readonly DELAYED: "delayed";
  readonly DELIVERED: "delivered";
  readonly EXPANDED: "expanded";
  readonly FAILED: "failed";
  readonly RELAYED: "relayed";
};
export type DsnAction = (typeof DsnAction)[keyof typeof DsnAction];
export interface ExtensionField {
  Name: string | undefined;
  Value: string | undefined;
}
export interface RecipientDsnFields {
  FinalRecipient?: string;
  Action: DsnAction | string | undefined;
  RemoteMta?: string;
  Status: string | undefined;
  DiagnosticCode?: string;
  LastAttemptDate?: Date;
  ExtensionFields?: ExtensionField[];
}
export interface BouncedRecipientInfo {
  Recipient: string | undefined;
  RecipientArn?: string;
  BounceType?: BounceType | string;
  RecipientDsnFields?: RecipientDsnFields;
}
export interface Destination {
  ToAddresses?: string[];
  CcAddresses?: string[];
  BccAddresses?: string[];
}
export interface MessageTag {
  Name: string | undefined;
  Value: string | undefined;
}
export interface BulkEmailDestination {
  Destination: Destination | undefined;
  ReplacementTags?: MessageTag[];
  ReplacementTemplateData?: string;
}
export declare const BulkEmailStatus: {
  readonly AccountDailyQuotaExceeded: "AccountDailyQuotaExceeded";
  readonly AccountSendingPaused: "AccountSendingPaused";
  readonly AccountSuspended: "AccountSuspended";
  readonly AccountThrottled: "AccountThrottled";
  readonly ConfigurationSetDoesNotExist: "ConfigurationSetDoesNotExist";
  readonly ConfigurationSetSendingPaused: "ConfigurationSetSendingPaused";
  readonly Failed: "Failed";
  readonly InvalidParameterValue: "InvalidParameterValue";
  readonly InvalidSendingPoolName: "InvalidSendingPoolName";
  readonly MailFromDomainNotVerified: "MailFromDomainNotVerified";
  readonly MessageRejected: "MessageRejected";
  readonly Success: "Success";
  readonly TemplateDoesNotExist: "TemplateDoesNotExist";
  readonly TransientFailure: "TransientFailure";
};
export type BulkEmailStatus =
  (typeof BulkEmailStatus)[keyof typeof BulkEmailStatus];
export interface BulkEmailDestinationStatus {
  Status?: BulkEmailStatus | string;
  Error?: string;
  MessageId?: string;
}
export declare class CannotDeleteException extends __BaseException {
  readonly name: "CannotDeleteException";
  readonly $fault: "client";
  Name?: string;
  constructor(
    opts: __ExceptionOptionType<CannotDeleteException, __BaseException>
  );
}
export interface CloneReceiptRuleSetRequest {
  RuleSetName: string | undefined;
  OriginalRuleSetName: string | undefined;
}
export interface CloneReceiptRuleSetResponse {}
export declare class LimitExceededException extends __BaseException {
  readonly name: "LimitExceededException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<LimitExceededException, __BaseException>
  );
}
export declare class RuleSetDoesNotExistException extends __BaseException {
  readonly name: "RuleSetDoesNotExistException";
  readonly $fault: "client";
  Name?: string;
  constructor(
    opts: __ExceptionOptionType<RuleSetDoesNotExistException, __BaseException>
  );
}
export declare const DimensionValueSource: {
  readonly EMAIL_HEADER: "emailHeader";
  readonly LINK_TAG: "linkTag";
  readonly MESSAGE_TAG: "messageTag";
};
export type DimensionValueSource =
  (typeof DimensionValueSource)[keyof typeof DimensionValueSource];
export interface CloudWatchDimensionConfiguration {
  DimensionName: string | undefined;
  DimensionValueSource: DimensionValueSource | string | undefined;
  DefaultDimensionValue: string | undefined;
}
export interface CloudWatchDestination {
  DimensionConfigurations: CloudWatchDimensionConfiguration[] | undefined;
}
export interface ConfigurationSet {
  Name: string | undefined;
}
export declare class ConfigurationSetAlreadyExistsException extends __BaseException {
  readonly name: "ConfigurationSetAlreadyExistsException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  constructor(
    opts: __ExceptionOptionType<
      ConfigurationSetAlreadyExistsException,
      __BaseException
    >
  );
}
export declare const ConfigurationSetAttribute: {
  readonly DELIVERY_OPTIONS: "deliveryOptions";
  readonly EVENT_DESTINATIONS: "eventDestinations";
  readonly REPUTATION_OPTIONS: "reputationOptions";
  readonly TRACKING_OPTIONS: "trackingOptions";
};
export type ConfigurationSetAttribute =
  (typeof ConfigurationSetAttribute)[keyof typeof ConfigurationSetAttribute];
export declare class ConfigurationSetDoesNotExistException extends __BaseException {
  readonly name: "ConfigurationSetDoesNotExistException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  constructor(
    opts: __ExceptionOptionType<
      ConfigurationSetDoesNotExistException,
      __BaseException
    >
  );
}
export declare class ConfigurationSetSendingPausedException extends __BaseException {
  readonly name: "ConfigurationSetSendingPausedException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  constructor(
    opts: __ExceptionOptionType<
      ConfigurationSetSendingPausedException,
      __BaseException
    >
  );
}
export interface CreateConfigurationSetRequest {
  ConfigurationSet: ConfigurationSet | undefined;
}
export interface CreateConfigurationSetResponse {}
export declare class InvalidConfigurationSetException extends __BaseException {
  readonly name: "InvalidConfigurationSetException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidConfigurationSetException,
      __BaseException
    >
  );
}
export interface KinesisFirehoseDestination {
  IAMRoleARN: string | undefined;
  DeliveryStreamARN: string | undefined;
}
export declare const EventType: {
  readonly BOUNCE: "bounce";
  readonly CLICK: "click";
  readonly COMPLAINT: "complaint";
  readonly DELIVERY: "delivery";
  readonly OPEN: "open";
  readonly REJECT: "reject";
  readonly RENDERING_FAILURE: "renderingFailure";
  readonly SEND: "send";
};
export type EventType = (typeof EventType)[keyof typeof EventType];
export interface SNSDestination {
  TopicARN: string | undefined;
}
export interface EventDestination {
  Name: string | undefined;
  Enabled?: boolean;
  MatchingEventTypes: (EventType | string)[] | undefined;
  KinesisFirehoseDestination?: KinesisFirehoseDestination;
  CloudWatchDestination?: CloudWatchDestination;
  SNSDestination?: SNSDestination;
}
export interface CreateConfigurationSetEventDestinationRequest {
  ConfigurationSetName: string | undefined;
  EventDestination: EventDestination | undefined;
}
export interface CreateConfigurationSetEventDestinationResponse {}
export declare class EventDestinationAlreadyExistsException extends __BaseException {
  readonly name: "EventDestinationAlreadyExistsException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  EventDestinationName?: string;
  constructor(
    opts: __ExceptionOptionType<
      EventDestinationAlreadyExistsException,
      __BaseException
    >
  );
}
export declare class InvalidCloudWatchDestinationException extends __BaseException {
  readonly name: "InvalidCloudWatchDestinationException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  EventDestinationName?: string;
  constructor(
    opts: __ExceptionOptionType<
      InvalidCloudWatchDestinationException,
      __BaseException
    >
  );
}
export declare class InvalidFirehoseDestinationException extends __BaseException {
  readonly name: "InvalidFirehoseDestinationException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  EventDestinationName?: string;
  constructor(
    opts: __ExceptionOptionType<
      InvalidFirehoseDestinationException,
      __BaseException
    >
  );
}
export declare class InvalidSNSDestinationException extends __BaseException {
  readonly name: "InvalidSNSDestinationException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  EventDestinationName?: string;
  constructor(
    opts: __ExceptionOptionType<InvalidSNSDestinationException, __BaseException>
  );
}
export interface TrackingOptions {
  CustomRedirectDomain?: string;
}
export interface CreateConfigurationSetTrackingOptionsRequest {
  ConfigurationSetName: string | undefined;
  TrackingOptions: TrackingOptions | undefined;
}
export interface CreateConfigurationSetTrackingOptionsResponse {}
export declare class InvalidTrackingOptionsException extends __BaseException {
  readonly name: "InvalidTrackingOptionsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidTrackingOptionsException,
      __BaseException
    >
  );
}
export declare class TrackingOptionsAlreadyExistsException extends __BaseException {
  readonly name: "TrackingOptionsAlreadyExistsException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  constructor(
    opts: __ExceptionOptionType<
      TrackingOptionsAlreadyExistsException,
      __BaseException
    >
  );
}
export interface CreateCustomVerificationEmailTemplateRequest {
  TemplateName: string | undefined;
  FromEmailAddress: string | undefined;
  TemplateSubject: string | undefined;
  TemplateContent: string | undefined;
  SuccessRedirectionURL: string | undefined;
  FailureRedirectionURL: string | undefined;
}
export declare class CustomVerificationEmailInvalidContentException extends __BaseException {
  readonly name: "CustomVerificationEmailInvalidContentException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      CustomVerificationEmailInvalidContentException,
      __BaseException
    >
  );
}
export declare class CustomVerificationEmailTemplateAlreadyExistsException extends __BaseException {
  readonly name: "CustomVerificationEmailTemplateAlreadyExistsException";
  readonly $fault: "client";
  CustomVerificationEmailTemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<
      CustomVerificationEmailTemplateAlreadyExistsException,
      __BaseException
    >
  );
}
export declare class FromEmailAddressNotVerifiedException extends __BaseException {
  readonly name: "FromEmailAddressNotVerifiedException";
  readonly $fault: "client";
  FromEmailAddress?: string;
  constructor(
    opts: __ExceptionOptionType<
      FromEmailAddressNotVerifiedException,
      __BaseException
    >
  );
}
export declare const ReceiptFilterPolicy: {
  readonly Allow: "Allow";
  readonly Block: "Block";
};
export type ReceiptFilterPolicy =
  (typeof ReceiptFilterPolicy)[keyof typeof ReceiptFilterPolicy];
export interface ReceiptIpFilter {
  Policy: ReceiptFilterPolicy | string | undefined;
  Cidr: string | undefined;
}
export interface ReceiptFilter {
  Name: string | undefined;
  IpFilter: ReceiptIpFilter | undefined;
}
export interface CreateReceiptFilterRequest {
  Filter: ReceiptFilter | undefined;
}
export interface CreateReceiptFilterResponse {}
export type InvocationType = "Event" | "RequestResponse";
export interface LambdaAction {
  TopicArn?: string;
  FunctionArn: string | undefined;
  InvocationType?: InvocationType | string;
}
export interface S3Action {
  TopicArn?: string;
  BucketName: string | undefined;
  ObjectKeyPrefix?: string;
  KmsKeyArn?: string;
}
export declare const SNSActionEncoding: {
  readonly Base64: "Base64";
  readonly UTF8: "UTF-8";
};
export type SNSActionEncoding =
  (typeof SNSActionEncoding)[keyof typeof SNSActionEncoding];
export interface SNSAction {
  TopicArn: string | undefined;
  Encoding?: SNSActionEncoding | string;
}
export declare const StopScope: {
  readonly RULE_SET: "RuleSet";
};
export type StopScope = (typeof StopScope)[keyof typeof StopScope];
export interface StopAction {
  Scope: StopScope | string | undefined;
  TopicArn?: string;
}
export interface WorkmailAction {
  TopicArn?: string;
  OrganizationArn: string | undefined;
}
export interface ReceiptAction {
  S3Action?: S3Action;
  BounceAction?: BounceAction;
  WorkmailAction?: WorkmailAction;
  LambdaAction?: LambdaAction;
  StopAction?: StopAction;
  AddHeaderAction?: AddHeaderAction;
  SNSAction?: SNSAction;
}
export declare const TlsPolicy: {
  readonly Optional: "Optional";
  readonly Require: "Require";
};
export type TlsPolicy = (typeof TlsPolicy)[keyof typeof TlsPolicy];
export interface ReceiptRule {
  Name: string | undefined;
  Enabled?: boolean;
  TlsPolicy?: TlsPolicy | string;
  Recipients?: string[];
  Actions?: ReceiptAction[];
  ScanEnabled?: boolean;
}
export interface CreateReceiptRuleRequest {
  RuleSetName: string | undefined;
  After?: string;
  Rule: ReceiptRule | undefined;
}
export interface CreateReceiptRuleResponse {}
export declare class InvalidLambdaFunctionException extends __BaseException {
  readonly name: "InvalidLambdaFunctionException";
  readonly $fault: "client";
  FunctionArn?: string;
  constructor(
    opts: __ExceptionOptionType<InvalidLambdaFunctionException, __BaseException>
  );
}
export declare class InvalidS3ConfigurationException extends __BaseException {
  readonly name: "InvalidS3ConfigurationException";
  readonly $fault: "client";
  Bucket?: string;
  constructor(
    opts: __ExceptionOptionType<
      InvalidS3ConfigurationException,
      __BaseException
    >
  );
}
export declare class InvalidSnsTopicException extends __BaseException {
  readonly name: "InvalidSnsTopicException";
  readonly $fault: "client";
  Topic?: string;
  constructor(
    opts: __ExceptionOptionType<InvalidSnsTopicException, __BaseException>
  );
}
export declare class RuleDoesNotExistException extends __BaseException {
  readonly name: "RuleDoesNotExistException";
  readonly $fault: "client";
  Name?: string;
  constructor(
    opts: __ExceptionOptionType<RuleDoesNotExistException, __BaseException>
  );
}
export interface CreateReceiptRuleSetRequest {
  RuleSetName: string | undefined;
}
export interface CreateReceiptRuleSetResponse {}
export interface Template {
  TemplateName: string | undefined;
  SubjectPart?: string;
  TextPart?: string;
  HtmlPart?: string;
}
export interface CreateTemplateRequest {
  Template: Template | undefined;
}
export interface CreateTemplateResponse {}
export declare class InvalidTemplateException extends __BaseException {
  readonly name: "InvalidTemplateException";
  readonly $fault: "client";
  TemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<InvalidTemplateException, __BaseException>
  );
}
export declare const CustomMailFromStatus: {
  readonly Failed: "Failed";
  readonly Pending: "Pending";
  readonly Success: "Success";
  readonly TemporaryFailure: "TemporaryFailure";
};
export type CustomMailFromStatus =
  (typeof CustomMailFromStatus)[keyof typeof CustomMailFromStatus];
export interface CustomVerificationEmailTemplate {
  TemplateName?: string;
  FromEmailAddress?: string;
  TemplateSubject?: string;
  SuccessRedirectionURL?: string;
  FailureRedirectionURL?: string;
}
export declare class CustomVerificationEmailTemplateDoesNotExistException extends __BaseException {
  readonly name: "CustomVerificationEmailTemplateDoesNotExistException";
  readonly $fault: "client";
  CustomVerificationEmailTemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<
      CustomVerificationEmailTemplateDoesNotExistException,
      __BaseException
    >
  );
}
export interface DeleteConfigurationSetRequest {
  ConfigurationSetName: string | undefined;
}
export interface DeleteConfigurationSetResponse {}
export interface DeleteConfigurationSetEventDestinationRequest {
  ConfigurationSetName: string | undefined;
  EventDestinationName: string | undefined;
}
export interface DeleteConfigurationSetEventDestinationResponse {}
export declare class EventDestinationDoesNotExistException extends __BaseException {
  readonly name: "EventDestinationDoesNotExistException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  EventDestinationName?: string;
  constructor(
    opts: __ExceptionOptionType<
      EventDestinationDoesNotExistException,
      __BaseException
    >
  );
}
export interface DeleteConfigurationSetTrackingOptionsRequest {
  ConfigurationSetName: string | undefined;
}
export interface DeleteConfigurationSetTrackingOptionsResponse {}
export declare class TrackingOptionsDoesNotExistException extends __BaseException {
  readonly name: "TrackingOptionsDoesNotExistException";
  readonly $fault: "client";
  ConfigurationSetName?: string;
  constructor(
    opts: __ExceptionOptionType<
      TrackingOptionsDoesNotExistException,
      __BaseException
    >
  );
}
export interface DeleteCustomVerificationEmailTemplateRequest {
  TemplateName: string | undefined;
}
export interface DeleteIdentityRequest {
  Identity: string | undefined;
}
export interface DeleteIdentityResponse {}
export interface DeleteIdentityPolicyRequest {
  Identity: string | undefined;
  PolicyName: string | undefined;
}
export interface DeleteIdentityPolicyResponse {}
export interface DeleteReceiptFilterRequest {
  FilterName: string | undefined;
}
export interface DeleteReceiptFilterResponse {}
export interface DeleteReceiptRuleRequest {
  RuleSetName: string | undefined;
  RuleName: string | undefined;
}
export interface DeleteReceiptRuleResponse {}
export interface DeleteReceiptRuleSetRequest {
  RuleSetName: string | undefined;
}
export interface DeleteReceiptRuleSetResponse {}
export interface DeleteTemplateRequest {
  TemplateName: string | undefined;
}
export interface DeleteTemplateResponse {}
export interface DeleteVerifiedEmailAddressRequest {
  EmailAddress: string | undefined;
}
export interface DeliveryOptions {
  TlsPolicy?: TlsPolicy | string;
}
export interface DescribeActiveReceiptRuleSetRequest {}
export interface ReceiptRuleSetMetadata {
  Name?: string;
  CreatedTimestamp?: Date;
}
export interface DescribeActiveReceiptRuleSetResponse {
  Metadata?: ReceiptRuleSetMetadata;
  Rules?: ReceiptRule[];
}
export interface DescribeConfigurationSetRequest {
  ConfigurationSetName: string | undefined;
  ConfigurationSetAttributeNames?: (ConfigurationSetAttribute | string)[];
}
export interface ReputationOptions {
  SendingEnabled?: boolean;
  ReputationMetricsEnabled?: boolean;
  LastFreshStart?: Date;
}
export interface DescribeConfigurationSetResponse {
  ConfigurationSet?: ConfigurationSet;
  EventDestinations?: EventDestination[];
  TrackingOptions?: TrackingOptions;
  DeliveryOptions?: DeliveryOptions;
  ReputationOptions?: ReputationOptions;
}
export interface DescribeReceiptRuleRequest {
  RuleSetName: string | undefined;
  RuleName: string | undefined;
}
export interface DescribeReceiptRuleResponse {
  Rule?: ReceiptRule;
}
export interface DescribeReceiptRuleSetRequest {
  RuleSetName: string | undefined;
}
export interface DescribeReceiptRuleSetResponse {
  Metadata?: ReceiptRuleSetMetadata;
  Rules?: ReceiptRule[];
}
export type VerificationStatus =
  | "Failed"
  | "NotStarted"
  | "Pending"
  | "Success"
  | "TemporaryFailure";
export interface IdentityDkimAttributes {
  DkimEnabled: boolean | undefined;
  DkimVerificationStatus: VerificationStatus | string | undefined;
  DkimTokens?: string[];
}
export interface GetAccountSendingEnabledResponse {
  Enabled?: boolean;
}
export interface GetCustomVerificationEmailTemplateRequest {
  TemplateName: string | undefined;
}
export interface GetCustomVerificationEmailTemplateResponse {
  TemplateName?: string;
  FromEmailAddress?: string;
  TemplateSubject?: string;
  TemplateContent?: string;
  SuccessRedirectionURL?: string;
  FailureRedirectionURL?: string;
}
export interface GetIdentityDkimAttributesRequest {
  Identities: string[] | undefined;
}
export interface GetIdentityDkimAttributesResponse {
  DkimAttributes: Record<string, IdentityDkimAttributes> | undefined;
}
export interface GetIdentityMailFromDomainAttributesRequest {
  Identities: string[] | undefined;
}
export interface IdentityMailFromDomainAttributes {
  MailFromDomain: string | undefined;
  MailFromDomainStatus: CustomMailFromStatus | string | undefined;
  BehaviorOnMXFailure: BehaviorOnMXFailure | string | undefined;
}
export interface GetIdentityMailFromDomainAttributesResponse {
  MailFromDomainAttributes:
    | Record<string, IdentityMailFromDomainAttributes>
    | undefined;
}
export interface GetIdentityNotificationAttributesRequest {
  Identities: string[] | undefined;
}
export interface IdentityNotificationAttributes {
  BounceTopic: string | undefined;
  ComplaintTopic: string | undefined;
  DeliveryTopic: string | undefined;
  ForwardingEnabled: boolean | undefined;
  HeadersInBounceNotificationsEnabled?: boolean;
  HeadersInComplaintNotificationsEnabled?: boolean;
  HeadersInDeliveryNotificationsEnabled?: boolean;
}
export interface GetIdentityNotificationAttributesResponse {
  NotificationAttributes:
    | Record<string, IdentityNotificationAttributes>
    | undefined;
}
export interface GetIdentityPoliciesRequest {
  Identity: string | undefined;
  PolicyNames: string[] | undefined;
}
export interface GetIdentityPoliciesResponse {
  Policies: Record<string, string> | undefined;
}
export interface GetIdentityVerificationAttributesRequest {
  Identities: string[] | undefined;
}
export interface IdentityVerificationAttributes {
  VerificationStatus: VerificationStatus | string | undefined;
  VerificationToken?: string;
}
export interface GetIdentityVerificationAttributesResponse {
  VerificationAttributes:
    | Record<string, IdentityVerificationAttributes>
    | undefined;
}
export interface GetSendQuotaResponse {
  Max24HourSend?: number;
  MaxSendRate?: number;
  SentLast24Hours?: number;
}
export interface SendDataPoint {
  Timestamp?: Date;
  DeliveryAttempts?: number;
  Bounces?: number;
  Complaints?: number;
  Rejects?: number;
}
export interface GetSendStatisticsResponse {
  SendDataPoints?: SendDataPoint[];
}
export interface GetTemplateRequest {
  TemplateName: string | undefined;
}
export interface GetTemplateResponse {
  Template?: Template;
}
export declare class TemplateDoesNotExistException extends __BaseException {
  readonly name: "TemplateDoesNotExistException";
  readonly $fault: "client";
  TemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<TemplateDoesNotExistException, __BaseException>
  );
}
export type IdentityType = "Domain" | "EmailAddress";
export declare class InvalidDeliveryOptionsException extends __BaseException {
  readonly name: "InvalidDeliveryOptionsException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      InvalidDeliveryOptionsException,
      __BaseException
    >
  );
}
export declare class InvalidPolicyException extends __BaseException {
  readonly name: "InvalidPolicyException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<InvalidPolicyException, __BaseException>
  );
}
export declare class InvalidRenderingParameterException extends __BaseException {
  readonly name: "InvalidRenderingParameterException";
  readonly $fault: "client";
  TemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<
      InvalidRenderingParameterException,
      __BaseException
    >
  );
}
export interface ListConfigurationSetsRequest {
  NextToken?: string;
  MaxItems?: number;
}
export interface ListConfigurationSetsResponse {
  ConfigurationSets?: ConfigurationSet[];
  NextToken?: string;
}
export interface ListCustomVerificationEmailTemplatesRequest {
  NextToken?: string;
  MaxResults?: number;
}
export interface ListCustomVerificationEmailTemplatesResponse {
  CustomVerificationEmailTemplates?: CustomVerificationEmailTemplate[];
  NextToken?: string;
}
export interface ListIdentitiesRequest {
  IdentityType?: IdentityType | string;
  NextToken?: string;
  MaxItems?: number;
}
export interface ListIdentitiesResponse {
  Identities: string[] | undefined;
  NextToken?: string;
}
export interface ListIdentityPoliciesRequest {
  Identity: string | undefined;
}
export interface ListIdentityPoliciesResponse {
  PolicyNames: string[] | undefined;
}
export interface ListReceiptFiltersRequest {}
export interface ListReceiptFiltersResponse {
  Filters?: ReceiptFilter[];
}
export interface ListReceiptRuleSetsRequest {
  NextToken?: string;
}
export interface ListReceiptRuleSetsResponse {
  RuleSets?: ReceiptRuleSetMetadata[];
  NextToken?: string;
}
export interface ListTemplatesRequest {
  NextToken?: string;
  MaxItems?: number;
}
export interface TemplateMetadata {
  Name?: string;
  CreatedTimestamp?: Date;
}
export interface ListTemplatesResponse {
  TemplatesMetadata?: TemplateMetadata[];
  NextToken?: string;
}
export interface ListVerifiedEmailAddressesResponse {
  VerifiedEmailAddresses?: string[];
}
export declare class MailFromDomainNotVerifiedException extends __BaseException {
  readonly name: "MailFromDomainNotVerifiedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      MailFromDomainNotVerifiedException,
      __BaseException
    >
  );
}
export interface Message {
  Subject: Content | undefined;
  Body: Body | undefined;
}
export interface MessageDsn {
  ReportingMta: string | undefined;
  ArrivalDate?: Date;
  ExtensionFields?: ExtensionField[];
}
export declare class MessageRejected extends __BaseException {
  readonly name: "MessageRejected";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<MessageRejected, __BaseException>);
}
export declare class MissingRenderingAttributeException extends __BaseException {
  readonly name: "MissingRenderingAttributeException";
  readonly $fault: "client";
  TemplateName?: string;
  constructor(
    opts: __ExceptionOptionType<
      MissingRenderingAttributeException,
      __BaseException
    >
  );
}
export type NotificationType = "Bounce" | "Complaint" | "Delivery";
export declare class ProductionAccessNotGrantedException extends __BaseException {
  readonly name: "ProductionAccessNotGrantedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<
      ProductionAccessNotGrantedException,
      __BaseException
    >
  );
}
export interface PutConfigurationSetDeliveryOptionsRequest {
  ConfigurationSetName: string | undefined;
  DeliveryOptions?: DeliveryOptions;
}
export interface PutConfigurationSetDeliveryOptionsResponse {}
export interface PutIdentityPolicyRequest {
  Identity: string | undefined;
  PolicyName: string | undefined;
  Policy: string | undefined;
}
export interface PutIdentityPolicyResponse {}
export interface RawMessage {
  Data: Uint8Array | undefined;
}
export interface ReorderReceiptRuleSetRequest {
  RuleSetName: string | undefined;
  RuleNames: string[] | undefined;
}
export interface ReorderReceiptRuleSetResponse {}
export interface SendBounceRequest {
  OriginalMessageId: string | undefined;
  BounceSender: string | undefined;
  Explanation?: string;
  MessageDsn?: MessageDsn;
  BouncedRecipientInfoList: BouncedRecipientInfo[] | undefined;
  BounceSenderArn?: string;
}
export interface SendBounceResponse {
  MessageId?: string;
}
export interface SendBulkTemplatedEmailRequest {
  Source: string | undefined;
  SourceArn?: string;
  ReplyToAddresses?: string[];
  ReturnPath?: string;
  ReturnPathArn?: string;
  ConfigurationSetName?: string;
  DefaultTags?: MessageTag[];
  Template: string | undefined;
  TemplateArn?: string;
  DefaultTemplateData?: string;
  Destinations: BulkEmailDestination[] | undefined;
}
export interface SendBulkTemplatedEmailResponse {
  Status: BulkEmailDestinationStatus[] | undefined;
}
export interface SendCustomVerificationEmailRequest {
  EmailAddress: string | undefined;
  TemplateName: string | undefined;
  ConfigurationSetName?: string;
}
export interface SendCustomVerificationEmailResponse {
  MessageId?: string;
}
export interface SendEmailRequest {
  Source: string | undefined;
  Destination: Destination | undefined;
  Message: Message | undefined;
  ReplyToAddresses?: string[];
  ReturnPath?: string;
  SourceArn?: string;
  ReturnPathArn?: string;
  Tags?: MessageTag[];
  ConfigurationSetName?: string;
}
export interface SendEmailResponse {
  MessageId: string | undefined;
}
export interface SendRawEmailRequest {
  Source?: string;
  Destinations?: string[];
  RawMessage: RawMessage | undefined;
  FromArn?: string;
  SourceArn?: string;
  ReturnPathArn?: string;
  Tags?: MessageTag[];
  ConfigurationSetName?: string;
}
export interface SendRawEmailResponse {
  MessageId: string | undefined;
}
export interface SendTemplatedEmailRequest {
  Source: string | undefined;
  Destination: Destination | undefined;
  ReplyToAddresses?: string[];
  ReturnPath?: string;
  SourceArn?: string;
  ReturnPathArn?: string;
  Tags?: MessageTag[];
  ConfigurationSetName?: string;
  Template: string | undefined;
  TemplateArn?: string;
  TemplateData: string | undefined;
}
export interface SendTemplatedEmailResponse {
  MessageId: string | undefined;
}
export interface SetActiveReceiptRuleSetRequest {
  RuleSetName?: string;
}
export interface SetActiveReceiptRuleSetResponse {}
export interface SetIdentityDkimEnabledRequest {
  Identity: string | undefined;
  DkimEnabled: boolean | undefined;
}
export interface SetIdentityDkimEnabledResponse {}
export interface SetIdentityFeedbackForwardingEnabledRequest {
  Identity: string | undefined;
  ForwardingEnabled: boolean | undefined;
}
export interface SetIdentityFeedbackForwardingEnabledResponse {}
export interface SetIdentityHeadersInNotificationsEnabledRequest {
  Identity: string | undefined;
  NotificationType: NotificationType | string | undefined;
  Enabled: boolean | undefined;
}
export interface SetIdentityHeadersInNotificationsEnabledResponse {}
export interface SetIdentityMailFromDomainRequest {
  Identity: string | undefined;
  MailFromDomain?: string;
  BehaviorOnMXFailure?: BehaviorOnMXFailure | string;
}
export interface SetIdentityMailFromDomainResponse {}
export interface SetIdentityNotificationTopicRequest {
  Identity: string | undefined;
  NotificationType: NotificationType | string | undefined;
  SnsTopic?: string;
}
export interface SetIdentityNotificationTopicResponse {}
export interface SetReceiptRulePositionRequest {
  RuleSetName: string | undefined;
  RuleName: string | undefined;
  After?: string;
}
export interface SetReceiptRulePositionResponse {}
export interface TestRenderTemplateRequest {
  TemplateName: string | undefined;
  TemplateData: string | undefined;
}
export interface TestRenderTemplateResponse {
  RenderedTemplate?: string;
}
export interface UpdateAccountSendingEnabledRequest {
  Enabled?: boolean;
}
export interface UpdateConfigurationSetEventDestinationRequest {
  ConfigurationSetName: string | undefined;
  EventDestination: EventDestination | undefined;
}
export interface UpdateConfigurationSetEventDestinationResponse {}
export interface UpdateConfigurationSetReputationMetricsEnabledRequest {
  ConfigurationSetName: string | undefined;
  Enabled: boolean | undefined;
}
export interface UpdateConfigurationSetSendingEnabledRequest {
  ConfigurationSetName: string | undefined;
  Enabled: boolean | undefined;
}
export interface UpdateConfigurationSetTrackingOptionsRequest {
  ConfigurationSetName: string | undefined;
  TrackingOptions: TrackingOptions | undefined;
}
export interface UpdateConfigurationSetTrackingOptionsResponse {}
export interface UpdateCustomVerificationEmailTemplateRequest {
  TemplateName: string | undefined;
  FromEmailAddress?: string;
  TemplateSubject?: string;
  TemplateContent?: string;
  SuccessRedirectionURL?: string;
  FailureRedirectionURL?: string;
}
export interface UpdateReceiptRuleRequest {
  RuleSetName: string | undefined;
  Rule: ReceiptRule | undefined;
}
export interface UpdateReceiptRuleResponse {}
export interface UpdateTemplateRequest {
  Template: Template | undefined;
}
export interface UpdateTemplateResponse {}
export interface VerifyDomainDkimRequest {
  Domain: string | undefined;
}
export interface VerifyDomainDkimResponse {
  DkimTokens: string[] | undefined;
}
export interface VerifyDomainIdentityRequest {
  Domain: string | undefined;
}
export interface VerifyDomainIdentityResponse {
  VerificationToken: string | undefined;
}
export interface VerifyEmailAddressRequest {
  EmailAddress: string | undefined;
}
export interface VerifyEmailIdentityRequest {
  EmailAddress: string | undefined;
}
export interface VerifyEmailIdentityResponse {}
