import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { SetReceiptRulePositionRequest, SetReceiptRulePositionResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link SetReceiptRulePositionCommand}.
 */
export interface SetReceiptRulePositionCommandInput extends SetReceiptRulePositionRequest {
}
/**
 * @public
 *
 * The output of {@link SetReceiptRulePositionCommand}.
 */
export interface SetReceiptRulePositionCommandOutput extends SetReceiptRulePositionResponse, __MetadataBearer {
}
/**
 * @public
 * <p>Sets the position of the specified receipt rule in the receipt rule set.</p>
 *         <p>For information about managing receipt rules, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email-managing-receipt-rules.html">Amazon SES
 *                 Developer Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, SetReceiptRulePositionCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, SetReceiptRulePositionCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // SetReceiptRulePositionRequest
 *   RuleSetName: "STRING_VALUE", // required
 *   RuleName: "STRING_VALUE", // required
 *   After: "STRING_VALUE",
 * };
 * const command = new SetReceiptRulePositionCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param SetReceiptRulePositionCommandInput - {@link SetReceiptRulePositionCommandInput}
 * @returns {@link SetReceiptRulePositionCommandOutput}
 * @see {@link SetReceiptRulePositionCommandInput} for command's `input` shape.
 * @see {@link SetReceiptRulePositionCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link RuleDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule does not exist.</p>
 *
 * @throws {@link RuleSetDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule set does not exist.</p>
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example SetReceiptRulePosition
 * ```javascript
 * // The following example sets the position of a receipt rule in a receipt rule set:
 * const input = {
 *   "After": "PutRuleAfterThisRule",
 *   "RuleName": "RuleToReposition",
 *   "RuleSetName": "MyRuleSet"
 * };
 * const command = new SetReceiptRulePositionCommand(input);
 * await client.send(command);
 * // example id: setreceiptruleposition-1469058530550
 * ```
 *
 */
export declare class SetReceiptRulePositionCommand extends $Command<SetReceiptRulePositionCommandInput, SetReceiptRulePositionCommandOutput, SESClientResolvedConfig> {
    readonly input: SetReceiptRulePositionCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: SetReceiptRulePositionCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<SetReceiptRulePositionCommandInput, SetReceiptRulePositionCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
