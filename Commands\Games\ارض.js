const { MessageEmbed, MessageActionRow, MessageButton, MessageSelectMenu } = require("discord.js");
const Data = require('pro.db');
const properties = require('./properties'); // استيراد العقارات من الملف المركزي

let activeMenus = {};
const cooldowns = new Map();
const COOLDOWN_TIME = 3600000; // وقت الانتظار بين جمع الأرباح (ساعة واحدة بالمللي ثانية)

// رموز الموارد
const RESOURCE_EMOJIS = {
  wood: "🪵", // خشب
  brick: "🧱", // طوب
  stone: "🪨" // حجر
};

module.exports = {
  name: "ارض",
  aliases: ["رض"],
  description: "شراء وإدارة العقارات",
  run: async (client, message, args) => {
    const userId = message.author.id;

    const setchannel = await Data.get(`chatbank_${message.guild.id}`);
    if (message.channel.id !== setchannel) return message.reply("**هذا الأمر لا يمكن استخدامه في هذه الشات**");

    if (activeMenus[userId]) {
      return message.reply("**قم بإكمال العملية السابقة أولاً**");
    }

    const colors = await Data.get(`bankcolor-${message.guild.id}`) || "#000"; // لون افتراضي

    // إنشاء رسالة بسيطة مع الصورة المطلوبة
    const embed = new MessageEmbed()
      .setColor(colors)
      .setDescription(`<@${message.author.id}>`)
      .setImage('https://i.imgur.com/JCm4UVr.png');

    // إضافة قائمة منسدلة لاختيار العقار للتحكم فيه
    const dropdownRow = new MessageActionRow()
      .addComponents(
        new MessageSelectMenu()
          .setCustomId('property_select')
          .setPlaceholder('اختر العقار للتحكم فيه')
          .addOptions(properties.map((property, index) => ({
            label: property.name,
            value: `property_${index}`,
            emoji: property.emoji
          })))
      );

    const messageEmbed = await message.reply({ embeds: [embed], components: [dropdownRow] });

    // تسجيل القائمة النشطة لهذا المستخدم
    activeMenus[userId] = {
      messageId: messageEmbed.id,
      channelId: message.channel.id,
      timestamp: Date.now()
    };

    // إنشاء معالج التفاعل
    const collector = messageEmbed.createMessageComponentCollector({
      filter: i => i.user.id === userId,
      time: 60000 // وقت انتهاء صلاحية القائمة (دقيقة واحدة)
    });

    collector.on('collect', async interaction => {
      // إذا تم اختيار عقار من القائمة المنسدلة
      if (interaction.customId === 'property_select') {
        const selectedIndex = parseInt(interaction.values[0].replace('property_', ''));
        const selectedProperty = properties[selectedIndex];

        // التحقق من صحة البيانات
        if (!selectedProperty) {
          return interaction.reply({
            content: "**حدث خطأ في تحديد العقار، يرجى المحاولة مرة أخرى**",
            ephemeral: true
          });
        }

        // فحص الرصيد والموارد المتاحة
        const woodCount = await Data.get(`wood_${userId}`) || 0;
        const brickCount = await Data.get(`brick_${userId}`) || 0;
        const stoneCount = await Data.get(`stone_${userId}`) || 0;
        const userBalance = await Data.get(`credits-${userId}`) || 0;

        // تنسيق الأرقام بفواصل
        const formattedBalance = userBalance.toLocaleString();
        const formattedPrice = selectedProperty.price.toLocaleString();
        const formattedWood = woodCount.toLocaleString();
        const formattedBrick = brickCount.toLocaleString();
        const formattedStone = stoneCount.toLocaleString();
        const formattedRequiredWood = selectedProperty.resources.wood.toLocaleString();
        const formattedRequiredBrick = selectedProperty.resources.brick.toLocaleString();
        const formattedRequiredStone = selectedProperty.resources.stone.toLocaleString();

        // إنشاء رسالة متطلبات الشراء (تظهر فقط للمستخدم الذي استخدم الأمر)
        const requirementsEmbed = new MessageEmbed()
          .setColor(colors)
          .setTitle(`${selectedProperty.emoji} ${selectedProperty.name}`)
          .setDescription(`**الموارد المطلوبة:**\n${RESOURCE_EMOJIS.wood} خشب x${formattedRequiredWood}\n${RESOURCE_EMOJIS.brick} طوب x${formattedRequiredBrick}\n${RESOURCE_EMOJIS.stone} حجر x${formattedRequiredStone}`);

        // أزرار الشراء والإلغاء
        const actionButtons = new MessageActionRow()
          .addComponents(
            new MessageButton()
              .setCustomId(`buy_${selectedIndex}`)
              .setLabel('شراء')
              .setStyle('SUCCESS')
              .setEmoji('💰'),
            new MessageButton()
              .setCustomId('cancel')
              .setLabel('إلغاء')
              .setStyle('DANGER')
              .setEmoji('❌')
          );

        await interaction.reply({
          embeds: [requirementsEmbed],
          components: [actionButtons],
          ephemeral: true
        });
      }

      // إذا تم النقر على زر الشراء
      if (interaction.customId.startsWith('buy_')) {
        const selectedIndex = parseInt(interaction.customId.replace('buy_', ''));
        const selectedProperty = properties[selectedIndex];

        // التحقق من صحة البيانات
        if (!selectedProperty) {
          return interaction.update({
            content: "**حدث خطأ في تحديد العقار، يرجى المحاولة مرة أخرى**",
            components: [],
            embeds: []
          });
        }

        // التحقق من امتلاك الموارد الكافية
        const woodCount = await Data.get(`wood_${userId}`) || 0;
        const brickCount = await Data.get(`brick_${userId}`) || 0;
        const stoneCount = await Data.get(`stone_${userId}`) || 0;
        const userBalance = await Data.get(`credits-${userId}`) || 0;

        // التحقق من كفاية الموارد
        if (woodCount < selectedProperty.resources.wood) {
          return interaction.update({
            content: `**لا تملك ما يكفي من الخشب. مطلوب: ${selectedProperty.resources.wood}، لديك: ${woodCount}**`,
            components: [],
            embeds: []
          });
        }

        if (brickCount < selectedProperty.resources.brick) {
          return interaction.update({
            content: `**لا تملك ما يكفي من الطوب. مطلوب: ${selectedProperty.resources.brick}، لديك: ${brickCount}**`,
            components: [],
            embeds: []
          });
        }

        if (stoneCount < selectedProperty.resources.stone) {
          return interaction.update({
            content: `**لا تملك ما يكفي من الحجر. مطلوب: ${selectedProperty.resources.stone}، لديك: ${stoneCount}**`,
            components: [],
            embeds: []
          });
        }

        if (userBalance < selectedProperty.price) {
          return interaction.update({
            content: `**رصيدك غير كافٍ. تحتاج إلى ${selectedProperty.price}$، ولديك ${userBalance}$**`,
            components: [],
            embeds: []
          });
        }

        // خصم الموارد والرصيد
        await Data.set(`wood_${userId}`, woodCount - selectedProperty.resources.wood);
        await Data.set(`brick_${userId}`, brickCount - selectedProperty.resources.brick);
        await Data.set(`stone_${userId}`, stoneCount - selectedProperty.resources.stone);
        await Data.set(`credits-${userId}`, userBalance - selectedProperty.price);

        // زيادة عدد العقارات المملوكة
        const ownedQuantity = await Data.get(`property_${userId}_${selectedProperty.name}`) || 0;
        await Data.set(`property_${userId}_${selectedProperty.name}`, ownedQuantity + 1);

        return interaction.update({
          content: `**تم شراء ${selectedProperty.emoji} ${selectedProperty.name} بنجاح!**\n**الربح: ${selectedProperty.profit}$ كل ساعة**`,
          components: [],
          embeds: []
        });
      }

      // إذا تم إلغاء الشراء
      if (interaction.customId === 'cancel') {
        return interaction.update({
          content: "**تم إلغاء عملية الشراء**",
          components: [],
          embeds: []
        });
      }
    });

    collector.on('end', () => {
      // إزالة القائمة النشطة عند انتهاء الوقت
      delete activeMenus[userId];
      // محاولة إزالة مكونات التفاعل من الرسالة إذا كانت لا تزال موجودة
      try {
        messageEmbed.edit({ components: [] }).catch(() => {});
      } catch (error) {
        console.error("Error removing components:", error);
      }
    });
  }
};
