import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { ListReceiptFiltersRequest, ListReceiptFiltersResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link ListReceiptFiltersCommand}.
 */
export interface ListReceiptFiltersCommandInput extends ListReceiptFiltersRequest {
}
/**
 * @public
 *
 * The output of {@link ListReceiptFiltersCommand}.
 */
export interface ListReceiptFiltersCommandOutput extends ListReceiptFiltersResponse, __MetadataBearer {
}
/**
 * @public
 * <p>Lists the IP address filters associated with your AWS account in the current AWS
 *             Region.</p>
 *         <p>For information about managing IP address filters, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email-managing-ip-filters.html">Amazon SES
 *                 Developer Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, ListReceiptFiltersCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, ListReceiptFiltersCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = {};
 * const command = new ListReceiptFiltersCommand(input);
 * const response = await client.send(command);
 * // { // ListReceiptFiltersResponse
 * //   Filters: [ // ReceiptFilterList
 * //     { // ReceiptFilter
 * //       Name: "STRING_VALUE", // required
 * //       IpFilter: { // ReceiptIpFilter
 * //         Policy: "STRING_VALUE", // required
 * //         Cidr: "STRING_VALUE", // required
 * //       },
 * //     },
 * //   ],
 * // };
 *
 * ```
 *
 * @param ListReceiptFiltersCommandInput - {@link ListReceiptFiltersCommandInput}
 * @returns {@link ListReceiptFiltersCommandOutput}
 * @see {@link ListReceiptFiltersCommandInput} for command's `input` shape.
 * @see {@link ListReceiptFiltersCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example ListReceiptFilters
 * ```javascript
 * // The following example lists the IP address filters that are associated with an AWS account:
 * const input = undefined;
 * const command = new ListReceiptFiltersCommand(input);
 * const response = await client.send(command);
 * /* response ==
 * {
 *   "Filters": [
 *     {
 *       "IpFilter": {
 *         "Cidr": "*******/24",
 *         "Policy": "Block"
 *       },
 *       "Name": "MyFilter"
 *     }
 *   ]
 * }
 * *\/
 * // example id: listreceiptfilters-*************
 * ```
 *
 */
export declare class ListReceiptFiltersCommand extends $Command<ListReceiptFiltersCommandInput, ListReceiptFiltersCommandOutput, SESClientResolvedConfig> {
    readonly input: ListReceiptFiltersCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: ListReceiptFiltersCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<ListReceiptFiltersCommandInput, ListReceiptFiltersCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
