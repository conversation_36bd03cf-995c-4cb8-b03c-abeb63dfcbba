import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import { <PERSON><PERSON>, HttpHandlerOptions as __HttpHandlerOptions, MetadataBearer as __MetadataBearer, MiddlewareStack } from "@aws-sdk/types";
import { ReorderReceiptRuleSetRequest, ReorderReceiptRuleSetResponse } from "../models/models_0";
import { ServiceInputTypes, ServiceOutputTypes, SESClientResolvedConfig } from "../SESClient";
/**
 * @public
 *
 * The input for {@link ReorderReceiptRuleSetCommand}.
 */
export interface ReorderReceiptRuleSetCommandInput extends ReorderReceiptRuleSetRequest {
}
/**
 * @public
 *
 * The output of {@link ReorderReceiptRuleSetCommand}.
 */
export interface ReorderReceiptRuleSetCommandOutput extends ReorderReceiptRuleSetResponse, __<PERSON>adataBearer {
}
/**
 * @public
 * <p>Reorders the receipt rules within a receipt rule set.</p>
 *         <note>
 *             <p>All of the rules in the rule set must be represented in this request. That is,
 *                 this API will return an error if the reorder request doesn't explicitly position all
 *                 of the rules.</p>
 *         </note>
 *         <p>For information about managing receipt rule sets, see the <a href="https://docs.aws.amazon.com/ses/latest/DeveloperGuide/receiving-email-managing-receipt-rule-sets.html">Amazon SES Developer Guide</a>.</p>
 *         <p>You can execute this operation no more than once per second.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SESClient, ReorderReceiptRuleSetCommand } from "@aws-sdk/client-ses"; // ES Modules import
 * // const { SESClient, ReorderReceiptRuleSetCommand } = require("@aws-sdk/client-ses"); // CommonJS import
 * const client = new SESClient(config);
 * const input = { // ReorderReceiptRuleSetRequest
 *   RuleSetName: "STRING_VALUE", // required
 *   RuleNames: [ // ReceiptRuleNamesList // required
 *     "STRING_VALUE",
 *   ],
 * };
 * const command = new ReorderReceiptRuleSetCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param ReorderReceiptRuleSetCommandInput - {@link ReorderReceiptRuleSetCommandInput}
 * @returns {@link ReorderReceiptRuleSetCommandOutput}
 * @see {@link ReorderReceiptRuleSetCommandInput} for command's `input` shape.
 * @see {@link ReorderReceiptRuleSetCommandOutput} for command's `response` shape.
 * @see {@link SESClientResolvedConfig | config} for SESClient's `config` shape.
 *
 * @throws {@link RuleDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule does not exist.</p>
 *
 * @throws {@link RuleSetDoesNotExistException} (client fault)
 *  <p>Indicates that the provided receipt rule set does not exist.</p>
 *
 * @throws {@link SESServiceException}
 * <p>Base exception class for all service exceptions from SES service.</p>
 *
 * @example ReorderReceiptRuleSet
 * ```javascript
 * // The following example reorders the receipt rules within a receipt rule set:
 * const input = {
 *   "RuleNames": [
 *     "MyRule",
 *     "MyOtherRule"
 *   ],
 *   "RuleSetName": "MyRuleSet"
 * };
 * const command = new ReorderReceiptRuleSetCommand(input);
 * await client.send(command);
 * // example id: reorderreceiptruleset-1469058156806
 * ```
 *
 */
export declare class ReorderReceiptRuleSetCommand extends $Command<ReorderReceiptRuleSetCommandInput, ReorderReceiptRuleSetCommandOutput, SESClientResolvedConfig> {
    readonly input: ReorderReceiptRuleSetCommandInput;
    static getEndpointParameterInstructions(): EndpointParameterInstructions;
    /**
     * @public
     */
    constructor(input: ReorderReceiptRuleSetCommandInput);
    /**
     * @internal
     */
    resolveMiddleware(clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>, configuration: SESClientResolvedConfig, options?: __HttpHandlerOptions): Handler<ReorderReceiptRuleSetCommandInput, ReorderReceiptRuleSetCommandOutput>;
    /**
     * @internal
     */
    private serialize;
    /**
     * @internal
     */
    private deserialize;
}
