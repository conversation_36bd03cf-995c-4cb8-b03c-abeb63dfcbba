const { Client, Collection } = require("discord.js");
const express = require('express');
const app = express();
const fs = require("fs");
const db = require('pro.db'); // تأكد من تعريف مكتبة pro.db

require("events").EventEmitter.defaultMaxListeners = 9999999;

const client = new Client({ intents: 32767 });
client.commands = new Collection();
client.slashCommands = new Collection();

app.get('/', (req, res) => {
  res.send('Hello Express app!');
});

app.listen(3000, () => {
  console.log('Server Started');
});

process.on("unhandledRejection", (reason, promise) => { return });
process.on("uncaughtException", (err, origin) => { return });
process.on('uncaughtExceptionMonitor', (err, origin) => { return });
process.on('multipleResolves', (type, promise, reason) => { return });

require('dotenv').config();
client.config = require(`${process.cwd()}/config`);
module.exports = client;

// Loading events
require("./events")(client);

// When the bot joins a new server
client.on('guildCreate', (guild) => {
  if (guild.id !== client.config.Guild) {
    guild.leave(); // Leave the guild if it's not the one specified
  }
});

// Bot login
client.login(client.config.token);

// When the bot is ready
client.once("ready", async () => {
  console.log(`Name : ${client.user.tag}\nPing : ${client.ws.ping}\nPrefix : ${client.config.prefix}\nID : ${client.user.id}\nServer : ${client.guilds.cache.size}\nMembers : ${client.users.cache.size}\nChannels : ${client.channels.cache.size}`);
  
  const botId = client.user.id;
  client.config.botId = `https://discord.com/oauth2/authorize?client_id=${botId}&permissions=8&scope=bot`;

  // Write the config file to disk
  fs.writeFile(`${process.cwd()}/config.json`, JSON.stringify(client.config, null, 4), (err) => {
    if (err) console.error(err);
  });
  
  // Retrieve saved status from the database
  let savedStatus = db.get(`${client.guilds.cache.first().id}_status`);
  let statusMessage = savedStatus ? savedStatus : "Sky Store";
  
  client.user.setPresence({
    activities: [{ name: statusMessage, type: 'STREAMING', url: 'https://twitch.tv/Sky-Store' }],
    status: 'online'
  });
});



