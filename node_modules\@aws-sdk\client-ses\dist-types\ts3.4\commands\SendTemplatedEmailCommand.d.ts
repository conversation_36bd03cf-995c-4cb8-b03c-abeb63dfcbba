import { EndpointParameterInstructions } from "@aws-sdk/middleware-endpoint";
import { Command as $Command } from "@aws-sdk/smithy-client";
import {
  Handler,
  HttpHandlerOptions as __HttpHandlerOptions,
  MetadataBearer as __MetadataBearer,
  MiddlewareStack,
} from "@aws-sdk/types";
import {
  SendTemplatedEmailRequest,
  SendTemplatedEmailResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SESClientResolvedConfig,
} from "../SESClient";
export interface SendTemplatedEmailCommandInput
  extends SendTemplatedEmailRequest {}
export interface SendTemplatedEmailCommandOutput
  extends SendTemplatedEmailResponse,
    __MetadataBearer {}
export declare class SendTemplatedEmailCommand extends $Command<
  SendTemplatedEmailCommandInput,
  SendTemplatedEmailCommandOutput,
  SESClientResolvedConfig
> {
  readonly input: SendTemplatedEmailCommandInput;
  static getEndpointParameterInstructions(): EndpointParameterInstructions;
  constructor(input: SendTemplatedEmailCommandInput);
  resolveMiddleware(
    clientStack: MiddlewareStack<ServiceInputTypes, ServiceOutputTypes>,
    configuration: SESClientResolvedConfig,
    options?: __HttpHandlerOptions
  ): Handler<SendTemplatedEmailCommandInput, SendTemplatedEmailCommandOutput>;
  private serialize;
  private deserialize;
}
